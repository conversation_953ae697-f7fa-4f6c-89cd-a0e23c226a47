/**
 * simplebar-react - v3.3.2
 * React component for SimpleBar
 * https://grsmto.github.io/simplebar/
 *
 * Made by <PERSON><PERSON>
 * Under MIT License
 */

(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory(require('react'), require('simplebar-core')) :
    typeof define === 'function' && define.amd ? define(['react', 'simplebar-core'], factory) :
    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.SimpleBarReact = factory(global.React, global.SimpleBarCore));
})(this, (function (React, SimpleBarCore) { 'use strict';

    function _interopNamespaceDefault(e) {
        var n = Object.create(null);
        if (e) {
            Object.keys(e).forEach(function (k) {
                if (k !== 'default') {
                    var d = Object.getOwnPropertyDescriptor(e, k);
                    Object.defineProperty(n, k, d.get ? d : {
                        enumerable: true,
                        get: function () { return e[k]; }
                    });
                }
            });
        }
        n.default = e;
        return Object.freeze(n);
    }

    var React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);

    /******************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */

    var __assign = function() {
        __assign = Object.assign || function __assign(t) {
            for (var s, i = 1, n = arguments.length; i < n; i++) {
                s = arguments[i];
                for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
            }
            return t;
        };
        return __assign.apply(this, arguments);
    };

    function __rest(s, e) {
        var t = {};
        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
            t[p] = s[p];
        if (s != null && typeof Object.getOwnPropertySymbols === "function")
            for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
                if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                    t[p[i]] = s[p[i]];
            }
        return t;
    }

    var SimpleBar = React__namespace.forwardRef(function (_a, ref) {
        var children = _a.children, _b = _a.scrollableNodeProps, scrollableNodeProps = _b === void 0 ? {} : _b, otherProps = __rest(_a, ["children", "scrollableNodeProps"]);
        var elRef = React__namespace.useRef();
        var scrollableNodeRef = React__namespace.useRef();
        var contentNodeRef = React__namespace.useRef();
        var options = {};
        var rest = {};
        Object.keys(otherProps).forEach(function (key) {
            if (Object.prototype.hasOwnProperty.call(SimpleBarCore.defaultOptions, key)) {
                options[key] = otherProps[key];
            }
            else {
                rest[key] = otherProps[key];
            }
        });
        var classNames = __assign(__assign({}, SimpleBarCore.defaultOptions.classNames), options.classNames);
        var scrollableNodeFullProps = __assign(__assign({}, scrollableNodeProps), { className: "".concat(classNames.contentWrapper).concat(scrollableNodeProps.className ? " ".concat(scrollableNodeProps.className) : ''), tabIndex: options.tabIndex || SimpleBarCore.defaultOptions.tabIndex, role: 'region', 'aria-label': options.ariaLabel || SimpleBarCore.defaultOptions.ariaLabel });
        React__namespace.useEffect(function () {
            var instance;
            scrollableNodeRef.current = scrollableNodeFullProps.ref
                ? scrollableNodeFullProps.ref.current
                : scrollableNodeRef.current;
            if (elRef.current) {
                instance = new SimpleBarCore(elRef.current, __assign(__assign(__assign({}, options), (scrollableNodeRef.current && {
                    scrollableNode: scrollableNodeRef.current
                })), (contentNodeRef.current && {
                    contentNode: contentNodeRef.current
                })));
                if (typeof ref === 'function') {
                    ref(instance);
                }
                else if (ref) {
                    ref.current = instance;
                }
            }
            return function () {
                instance === null || instance === void 0 ? void 0 : instance.unMount();
                instance = null;
                if (typeof ref === 'function') {
                    ref(null);
                }
            };
        }, []);
        return (React__namespace.createElement("div", __assign({ "data-simplebar": "init", ref: elRef }, rest),
            React__namespace.createElement("div", { className: classNames.wrapper },
                React__namespace.createElement("div", { className: classNames.heightAutoObserverWrapperEl },
                    React__namespace.createElement("div", { className: classNames.heightAutoObserverEl })),
                React__namespace.createElement("div", { className: classNames.mask },
                    React__namespace.createElement("div", { className: classNames.offset }, typeof children === 'function' ? (children({
                        scrollableNodeRef: scrollableNodeRef,
                        scrollableNodeProps: __assign(__assign({}, scrollableNodeFullProps), { ref: scrollableNodeRef }),
                        contentNodeRef: contentNodeRef,
                        contentNodeProps: {
                            className: classNames.contentEl,
                            ref: contentNodeRef
                        }
                    })) : (React__namespace.createElement("div", __assign({}, scrollableNodeFullProps),
                        React__namespace.createElement("div", { className: classNames.contentEl }, children))))),
                React__namespace.createElement("div", { className: classNames.placeholder })),
            React__namespace.createElement("div", { className: "".concat(classNames.track, " ").concat(classNames.horizontal) },
                React__namespace.createElement("div", { className: classNames.scrollbar })),
            React__namespace.createElement("div", { className: "".concat(classNames.track, " ").concat(classNames.vertical) },
                React__namespace.createElement("div", { className: classNames.scrollbar }))));
    });
    SimpleBar.displayName = 'SimpleBar';

    return SimpleBar;

}));
//# sourceMappingURL=index.cjs.map
