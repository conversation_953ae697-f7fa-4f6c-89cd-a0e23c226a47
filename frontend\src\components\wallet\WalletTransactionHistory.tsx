import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Table,
  Button,
  Form,
  InputGroup,
  Alert,
  Spinner,
  Badge,
  Pagination,
} from 'react-bootstrap';
import creditService, { CreditTransaction } from '../../services/creditService';
import dattaAbleTheme from '../../theme/dattaAbleTheme';

interface WalletTransactionHistoryProps {
  refreshTrigger: number;
}

const WalletTransactionHistory: React.FC<WalletTransactionHistoryProps> = ({
  refreshTrigger,
}) => {
  const [transactions, setTransactions] = useState<CreditTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [refreshing, setRefreshing] = useState(false);

  const fetchTransactions = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      const response = await creditService.getTransactions();
      setTransactions(response.transactions.data);
    } catch (error) {
      console.error('Failed to fetch transactions:', error);
    } finally {
      setLoading(false);
      if (showRefreshing) setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, [refreshTrigger]);

  const handleRefresh = () => {
    fetchTransactions(true);
  };

  const getTransactionIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'credit':
      case 'top-up':
      case 'purchase':
        return 'fas fa-arrow-up text-success';
      case 'debit':
      case 'usage':
      case 'spend':
        return 'fas fa-arrow-down text-danger';
      default:
        return 'fas fa-receipt text-primary';
    }
  };

  const getTransactionVariant = (type: string) => {
    switch (type.toLowerCase()) {
      case 'credit':
      case 'top-up':
      case 'purchase':
        return 'success';
      case 'debit':
      case 'usage':
      case 'spend':
        return 'danger';
      default:
        return 'primary';
    }
  };

  const formatTransactionAmount = (transaction: CreditTransaction) => {
    const amount = transaction.amount_paid || transaction.credit_amount;
    const formattedAmount = creditService.formatWalletBalance(Math.abs(amount));
    const isCredit = transaction.is_credit;
    return isCredit ? `+${formattedAmount}` : `-${formattedAmount}`;
  };

  // Filter transactions
  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || transaction.type.toLowerCase() === filterType.toLowerCase();
    return matchesSearch && matchesFilter;
  });

  // Paginate transactions
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentTransactions = filteredTransactions.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '300px' }}>
        <Spinner animation="border" variant="primary" />
      </div>
    );
  }

  return (
    <div>
      <div className="d-flex align-items-center justify-content-between mb-4">
        <div className="d-flex align-items-center gap-3">
          <div 
            className="d-flex align-items-center justify-content-center"
            style={{
              width: '48px',
              height: '48px',
              borderRadius: dattaAbleTheme.borderRadius.lg,
              backgroundColor: `${dattaAbleTheme.colors.primary.main}20`,
              color: dattaAbleTheme.colors.primary.main,
            }}
          >
            <i className="fas fa-history"></i>
          </div>
          <h5 className="mb-0 fw-semibold">Transaction History</h5>
        </div>
        <Button
          variant="outline-primary"
          size="sm"
          onClick={handleRefresh}
          disabled={refreshing}
          className="d-flex align-items-center gap-2"
        >
          {refreshing ? (
            <Spinner animation="border" size="sm" />
          ) : (
            <i className="fas fa-sync-alt"></i>
          )}
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card className="border-0 shadow-sm mb-4" style={{ borderRadius: dattaAbleTheme.borderRadius.lg }}>
        <Card.Body className="p-3">
          <Row className="g-3 align-items-center">
            <Col md={4}>
              <InputGroup>
                <InputGroup.Text>
                  <i className="fas fa-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search transactions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
            <Col md={4}>
              <InputGroup>
                <InputGroup.Text>
                  <i className="fas fa-filter"></i>
                </InputGroup.Text>
                <Form.Select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                >
                  <option value="all">All Types</option>
                  <option value="credit">Credits</option>
                  <option value="debit">Debits</option>
                  <option value="top-up">Top-ups</option>
                  <option value="usage">Usage</option>
                </Form.Select>
              </InputGroup>
            </Col>
            <Col md={4}>
              <small className="text-muted">
                Showing {filteredTransactions.length} of {transactions.length} transactions
              </small>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Transactions Table */}
      {filteredTransactions.length === 0 ? (
        <Alert variant="info" className="text-center" style={{ borderRadius: dattaAbleTheme.borderRadius.lg }}>
          <div className="py-4">
            <i className="fas fa-info-circle fa-2x mb-3 text-muted"></i>
            <h6 className="fw-semibold mb-2">No transactions found</h6>
            <p className="mb-0 text-muted">
              {searchTerm || filterType !== 'all'
                ? 'Try adjusting your search or filter criteria.'
                : 'Your transaction history will appear here once you start using your wallet.'}
            </p>
          </div>
        </Alert>
      ) : (
        <Card className="border-0 shadow-sm" style={{ borderRadius: dattaAbleTheme.borderRadius.lg }}>
          <div className="table-responsive">
            <Table className="mb-0">
              <thead className="bg-light">
                <tr>
                  <th className="fw-semibold py-3 border-0">Type</th>
                  <th className="fw-semibold py-3 border-0">Description</th>
                  <th className="fw-semibold py-3 border-0 text-end">Amount</th>
                  <th className="fw-semibold py-3 border-0 text-end">Date</th>
                  <th className="fw-semibold py-3 border-0 text-center d-none d-md-table-cell">Status</th>
                </tr>
              </thead>
              <tbody>
                {currentTransactions.map((transaction, index) => (
                  <tr key={transaction.id || index} className="border-bottom">
                    <td className="py-3 border-0">
                      <div className="d-flex align-items-center gap-2">
                        <i className={getTransactionIcon(transaction.type)}></i>
                        <span className="fw-semibold small">{transaction.type}</span>
                      </div>
                    </td>
                    <td className="py-3 border-0">
                      <span className="small">{transaction.description}</span>
                    </td>
                    <td className="py-3 border-0 text-end">
                      <span 
                        className={`fw-semibold small text-${getTransactionVariant(transaction.type)}`}
                      >
                        {formatTransactionAmount(transaction)}
                      </span>
                    </td>
                    <td className="py-3 border-0 text-end">
                      <small className="text-muted">
                        {new Date(transaction.created_at).toLocaleDateString('en-MY', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                        })}
                      </small>
                    </td>
                    <td className="py-3 border-0 text-center d-none d-md-table-cell">
                      <Badge bg="success" className="small">
                        Completed
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <Card.Footer className="bg-light border-0 d-flex justify-content-between align-items-center">
              <small className="text-muted">
                Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, filteredTransactions.length)} of {filteredTransactions.length} entries
              </small>
              <Pagination className="mb-0">
                <Pagination.Prev 
                  disabled={currentPage === 1}
                  onClick={() => handlePageChange(currentPage - 1)}
                />
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = currentPage <= 3 ? i + 1 : currentPage - 2 + i;
                  if (pageNum > totalPages) return null;
                  return (
                    <Pagination.Item
                      key={pageNum}
                      active={pageNum === currentPage}
                      onClick={() => handlePageChange(pageNum)}
                    >
                      {pageNum}
                    </Pagination.Item>
                  );
                })}
                <Pagination.Next 
                  disabled={currentPage === totalPages}
                  onClick={() => handlePageChange(currentPage + 1)}
                />
              </Pagination>
            </Card.Footer>
          )}
        </Card>
      )}
    </div>
  );
};

export default WalletTransactionHistory;
