{"ast": null, "code": "import React from'react';import DattaAbleLayout from'./DattaAbleLayout';import ProtectedRoute from'../auth/ProtectedRoute';import{jsx as _jsx}from\"react/jsx-runtime\";const DashboardRoute=_ref=>{let{children}=_ref;return/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(DattaAbleLayout,{children:children})});};export default DashboardRoute;", "map": {"version": 3, "names": ["React", "DattaAbleLayout", "ProtectedRoute", "jsx", "_jsx", "DashboardRoute", "_ref", "children"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/DashboardRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport DattaAbleLayout from './DattaAbleLayout';\nimport ProtectedRoute from '../auth/ProtectedRoute';\n\ninterface DashboardRouteProps {\n  children: React.ReactNode;\n}\n\nconst DashboardRoute: React.FC<DashboardRouteProps> = ({ children }) => {\n  return (\n    <ProtectedRoute>\n      <DattaAbleLayout>\n        {children}\n      </DattaAbleLayout>\n    </ProtectedRoute>\n  );\n};\n\nexport default DashboardRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAMpD,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACjE,mBACEF,IAAA,CAACF,cAAc,EAAAK,QAAA,cACbH,IAAA,CAACH,eAAe,EAAAM,QAAA,CACbA,QAAQ,CACM,CAAC,CACJ,CAAC,CAErB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}