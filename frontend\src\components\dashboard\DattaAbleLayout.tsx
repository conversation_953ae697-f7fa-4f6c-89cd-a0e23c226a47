import React, { useState, useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { useLocation } from 'react-router-dom';
import Da<PERSON><PERSON>bleHeader from './DattaAbleHeader';
import DattaAbleSidebar from './DattaAbleSidebar';
import DattaAbleFooter from './DattaAbleFooter';
import DattaAbleBreadcrumbs from './DattaAbleBreadcrumbs';
import dattaAbleTheme from '../../theme/dattaAbleTheme';
import 'bootstrap/dist/css/bootstrap.min.css';
import '@fontsource/open-sans/300.css';
import '@fontsource/open-sans/400.css';
import '@fontsource/open-sans/500.css';
import '@fontsource/open-sans/600.css';
import '@fontsource/open-sans/700.css';

interface DattaAbleLayoutProps {
  children: React.ReactNode;
}

const DattaAbleLayout: React.FC<DattaAbleLayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const location = useLocation();

  // Close sidebar on route change (mobile)
  useEffect(() => {
    setSidebarOpen(false);
  }, [location.pathname]);

  // Apply CSS variables to document root
  useEffect(() => {
    const root = document.documentElement;
    Object.entries(dattaAbleTheme.cssVariables).forEach(([key, value]) => {
      root.style.setProperty(key, value);
    });
  }, []);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const toggleSidebarCollapse = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const layoutStyles = {
    minHeight: '100vh',
    backgroundColor: dattaAbleTheme.colors.background.default,
    fontFamily: dattaAbleTheme.typography.fontFamily,
  };

  const mainContentStyles = {
    marginLeft: sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width,
    transition: 'margin-left 0.3s ease',
    minHeight: '100vh',
    display: 'flex',
    flexDirection: 'column',
  };

  const contentWrapperStyles = {
    flex: 1,
    padding: dattaAbleTheme.spacing[4],
    paddingTop: `calc(${dattaAbleTheme.layout.header.height} + ${dattaAbleTheme.spacing[4]})`,
  };

  return (
    <div style={layoutStyles}>
      {/* Sidebar */}
      <DattaAbleSidebar
        isOpen={sidebarOpen}
        isCollapsed={sidebarCollapsed}
        onToggle={toggleSidebar}
        onCollapse={toggleSidebarCollapse}
      />

      {/* Main Content Area */}
      <div 
        style={{
          ...mainContentStyles,
          marginLeft: window.innerWidth < 768 ? 0 : (sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width)
        }}
      >
        {/* Header */}
        <DattaAbleHeader
          onToggleSidebar={toggleSidebar}
          onToggleSidebarCollapse={toggleSidebarCollapse}
          sidebarCollapsed={sidebarCollapsed}
        />

        {/* Content Wrapper */}
        <div style={contentWrapperStyles}>
          {/* Breadcrumbs */}
          <DattaAbleBreadcrumbs />

          {/* Main Content */}
          <Container fluid className="px-0">
            <Row>
              <Col>
                {children}
              </Col>
            </Row>
          </Container>
        </div>

        {/* Footer */}
        <DattaAbleFooter />
      </div>

      {/* Mobile Overlay */}
      {sidebarOpen && window.innerWidth < 768 && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1040,
          }}
          onClick={toggleSidebar}
        />
      )}

      {/* Custom Styles */}
      <style jsx>{`
        /* Custom scrollbar */
        ::-webkit-scrollbar {
          width: 6px;
        }
        
        ::-webkit-scrollbar-track {
          background: ${dattaAbleTheme.colors.background.light};
        }
        
        ::-webkit-scrollbar-thumb {
          background: ${dattaAbleTheme.colors.text.secondary};
          border-radius: ${dattaAbleTheme.borderRadius.full};
        }
        
        ::-webkit-scrollbar-thumb:hover {
          background: ${dattaAbleTheme.colors.text.primary};
        }

        /* Responsive adjustments */
        @media (max-width: 767.98px) {
          .main-content {
            margin-left: 0 !important;
          }
        }

        /* Animation classes */
        .fade-in {
          animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        /* Bootstrap overrides for Datta Able styling */
        .card {
          border-radius: ${dattaAbleTheme.borderRadius.lg};
          box-shadow: ${dattaAbleTheme.shadows.sm};
          border: 1px solid ${dattaAbleTheme.colors.border};
        }

        .btn {
          border-radius: ${dattaAbleTheme.borderRadius.md};
          font-weight: ${dattaAbleTheme.typography.fontWeight.medium};
        }

        .form-control {
          border-radius: ${dattaAbleTheme.borderRadius.md};
          border: 1px solid ${dattaAbleTheme.colors.border};
        }

        .form-control:focus {
          border-color: ${dattaAbleTheme.colors.primary.main};
          box-shadow: 0 0 0 0.2rem ${dattaAbleTheme.colors.primary.main}25;
        }

        /* Custom utility classes */
        .text-primary-custom {
          color: ${dattaAbleTheme.colors.primary.main} !important;
        }

        .bg-primary-custom {
          background-color: ${dattaAbleTheme.colors.primary.main} !important;
        }

        .border-primary-custom {
          border-color: ${dattaAbleTheme.colors.primary.main} !important;
        }

        .shadow-custom {
          box-shadow: ${dattaAbleTheme.shadows.md} !important;
        }

        /* Wallet specific styling */
        .wallet-card {
          background: linear-gradient(135deg, ${dattaAbleTheme.colors.primary.main} 0%, ${dattaAbleTheme.colors.primary.dark} 100%);
          color: white;
          border-radius: ${dattaAbleTheme.borderRadius['2xl']};
          box-shadow: ${dattaAbleTheme.shadows.lg};
        }

        .wallet-balance {
          font-size: ${dattaAbleTheme.typography.fontSize['4xl']};
          font-weight: ${dattaAbleTheme.typography.fontWeight.bold};
        }

        .stat-card {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
          transform: translateY(-4px);
          box-shadow: ${dattaAbleTheme.shadows.lg};
        }
      `}</style>
    </div>
  );
};

export default DattaAbleLayout;
