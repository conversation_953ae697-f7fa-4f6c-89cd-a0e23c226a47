import React from 'react';
import { Breadcrumb } from 'react-bootstrap';
import { useLocation, Link } from 'react-router-dom';
import dattaAbleTheme from '../../theme/dattaAbleTheme';

const DattaAbleBreadcrumbs = () => {
  const location = useLocation();
  
  // Define breadcrumb mappings
  const breadcrumbMap = {
    '/dashboard': 'Dashboard',
    '/dashboard/wallet': 'Wallet',
    '/dashboard/order': 'Order',
    '/dashboard/orders': 'My Orders',
  };

  // Generate breadcrumb items
  const generateBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(segment => segment);
    const breadcrumbs = [];
    
    // Always start with Dashboard
    breadcrumbs.push({
      path: '/dashboard',
      label: 'Dashboard',
      isActive: location.pathname === '/dashboard'
    });

    // Add current page if not dashboard
    if (location.pathname !== '/dashboard') {
      const currentLabel = breadcrumbMap[location.pathname] || 'Page';
      breadcrumbs.push({
        path: location.pathname,
        label: currentLabel,
        isActive: true
      });
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  const breadcrumbStyles = {
    backgroundColor: 'transparent',
    padding: `0 0 ${dattaAbleTheme.spacing[4]}`,
    margin: 0,
    fontSize: dattaAbleTheme.typography.fontSize.sm,
  };

  const breadcrumbItemStyles = {
    color: dattaAbleTheme.colors.text.secondary,
  };

  const activeBreadcrumbStyles = {
    color: dattaAbleTheme.colors.text.primary,
    fontWeight: dattaAbleTheme.typography.fontWeight.medium,
  };

  const linkStyles = {
    color: dattaAbleTheme.colors.primary.main,
    textDecoration: 'none',
  };

  // Don't show breadcrumbs if only one item (Dashboard)
  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <Breadcrumb style={breadcrumbStyles}>
      {breadcrumbs.map((crumb, index) => (
        <Breadcrumb.Item
          key={crumb.path}
          active={crumb.isActive}
          style={crumb.isActive ? activeBreadcrumbStyles : breadcrumbItemStyles}
        >
          {crumb.isActive ? (
            crumb.label
          ) : (
            <Link
              to={crumb.path}
              style={linkStyles}
              onMouseEnter={(e) => {
                e.target.style.textDecoration = 'underline';
              }}
              onMouseLeave={(e) => {
                e.target.style.textDecoration = 'none';
              }}
            >
              {crumb.label}
            </Link>
          )}
        </Breadcrumb.Item>
      ))}

      <style jsx>{`
        .breadcrumb-item + .breadcrumb-item::before {
          content: '/';
          color: ${dattaAbleTheme.colors.text.secondary};
        }

        .breadcrumb-item.active {
          color: ${dattaAbleTheme.colors.text.primary};
        }
      `}</style>
    </Breadcrumb>
  );
};

export default DattaAbleBreadcrumbs;
