{"version": "1.3.2", "name": "simplebar-core", "title": "SimpleBar.js", "description": "Scrollbars, simpler.", "files": ["dist", "README.md"], "author": "<PERSON><PERSON> from a fork by <PERSON>", "repository": {"type": "git", "url": "https://github.com/grsmto/simplebar.git", "directory": "packages/simplebar-core"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "style": "./dist/simplebar.min.css", "typings": "./dist/index.d.ts", "sideEffects": ["*.css"], "homepage": "https://grsmto.github.io/simplebar/", "bugs": "https://github.com/grsmto/simplebar/issues", "license": "MIT", "scripts": {"serve": "serve -s demo", "build": "rollup -c && cp src/simplebar.css dist/simplebar.css", "dev": "rollup -c -w --environment BUILD:development", "test": "yarn test:unit", "test:unit": "jest -c jest-unit.config.js", "test:e2e": "env-cmd --silent jest -c jest-e2e.config.js --runInBand", "version": "yarn build", "precommit": "lint-staged"}, "dependencies": {"lodash": "^4.17.21", "lodash-es": "^4.17.21"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "browserstack-local": "^1.5.1"}, "lint-staged": {"*.{js,jsx,json}": ["prettier-eslint --write", "git add"]}, "gitHead": "46a2c4a8f5b0a4ed66f80dcd4d2a74ee52ce1ff3"}