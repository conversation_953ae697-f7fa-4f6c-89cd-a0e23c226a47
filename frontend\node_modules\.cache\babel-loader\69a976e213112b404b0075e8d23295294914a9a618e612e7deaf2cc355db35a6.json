{"ast": null, "code": "// Datta Able Theme Configuration\n// This replaces the Material-UI theme with Bootstrap-based styling\n// Color palette based on Datta Able design\nexport const colors={primary:{main:'#1976d2',light:'#42a5f5',dark:'#1565c0',contrastText:'#ffffff'},secondary:{main:'#dc004e',light:'#ff5983',dark:'#9a0036',contrastText:'#ffffff'},success:{main:'#4caf50',light:'#81c784',dark:'#388e3c',contrastText:'#ffffff'},warning:{main:'#ff9800',light:'#ffb74d',dark:'#f57c00',contrastText:'#ffffff'},error:{main:'#f44336',light:'#e57373',dark:'#d32f2f',contrastText:'#ffffff'},info:{main:'#2196f3',light:'#64b5f6',dark:'#1976d2',contrastText:'#ffffff'},background:{default:'#f8f9fa',paper:'#ffffff',light:'#f5f5f5'},text:{primary:'#212529',secondary:'#6c757d',disabled:'#adb5bd'},divider:'#dee2e6',border:'#e9ecef'};// Typography configuration\nexport const typography={fontFamily:'\"Open Sans\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',fontSize:{xs:'0.75rem',// 12px\nsm:'0.875rem',// 14px\nbase:'1rem',// 16px\nlg:'1.125rem',// 18px\nxl:'1.25rem',// 20px\n'2xl':'1.5rem',// 24px\n'3xl':'1.875rem',// 30px\n'4xl':'2.25rem',// 36px\n'5xl':'3rem'// 48px\n},fontWeight:{light:300,normal:400,medium:500,semibold:600,bold:700},lineHeight:{tight:1.25,normal:1.5,relaxed:1.75}};// Spacing configuration (based on Bootstrap's spacing scale)\nexport const spacing={0:'0',1:'0.25rem',// 4px\n2:'0.5rem',// 8px\n3:'0.75rem',// 12px\n4:'1rem',// 16px\n5:'1.25rem',// 20px\n6:'1.5rem',// 24px\n8:'2rem',// 32px\n10:'2.5rem',// 40px\n12:'3rem',// 48px\n16:'4rem',// 64px\n20:'5rem',// 80px\n24:'6rem'// 96px\n};// Border radius configuration\nexport const borderRadius={none:'0',sm:'0.125rem',// 2px\nbase:'0.25rem',// 4px\nmd:'0.375rem',// 6px\nlg:'0.5rem',// 8px\nxl:'0.75rem',// 12px\n'2xl':'1rem',// 16px\n'3xl':'1.5rem',// 24px\nfull:'9999px'};// Shadow configuration\nexport const shadows={sm:'0 1px 2px 0 rgba(0, 0, 0, 0.05)',base:'0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',md:'0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',lg:'0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',xl:'0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)','2xl':'0 25px 50px -12px rgba(0, 0, 0, 0.25)',inner:'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)'};// Breakpoints (Bootstrap breakpoints)\nexport const breakpoints={xs:'0px',sm:'576px',md:'768px',lg:'992px',xl:'1200px',xxl:'1400px'};// Layout configuration\nexport const layout={sidebar:{width:'260px',collapsedWidth:'70px'},header:{height:'70px'},footer:{height:'60px'}};// Component styles\nexport const components={card:{borderRadius:borderRadius.lg,boxShadow:shadows.sm,border:`1px solid ${colors.border}`},button:{borderRadius:borderRadius.md,fontWeight:typography.fontWeight.medium,padding:{sm:`${spacing[2]} ${spacing[3]}`,md:`${spacing[3]} ${spacing[4]}`,lg:`${spacing[4]} ${spacing[6]}`}},input:{borderRadius:borderRadius.md,border:`1px solid ${colors.border}`,padding:`${spacing[3]} ${spacing[4]}`},modal:{borderRadius:borderRadius.lg,boxShadow:shadows.xl}};// Utility functions\nexport const getColor=colorPath=>{const keys=colorPath.split('.');let result=colors;for(const key of keys){result=result[key];if(!result)return colors.primary.main;// fallback\n}return result;};export const getSpacing=value=>{if(typeof value==='number'){return spacing[value]||`${value*0.25}rem`;}return spacing[value]||value;};// CSS custom properties for dynamic theming\nexport const cssVariables={'--color-primary':colors.primary.main,'--color-primary-light':colors.primary.light,'--color-primary-dark':colors.primary.dark,'--color-secondary':colors.secondary.main,'--color-success':colors.success.main,'--color-warning':colors.warning.main,'--color-error':colors.error.main,'--color-info':colors.info.main,'--color-background':colors.background.default,'--color-paper':colors.background.paper,'--color-text-primary':colors.text.primary,'--color-text-secondary':colors.text.secondary,'--color-border':colors.border,'--font-family':typography.fontFamily,'--sidebar-width':layout.sidebar.width,'--header-height':layout.header.height,'--border-radius':borderRadius.md,'--shadow-sm':shadows.sm,'--shadow-md':shadows.md,'--shadow-lg':shadows.lg};// Export default theme object\nconst dattaAbleTheme={colors,typography,spacing,borderRadius,shadows,breakpoints,layout,components,cssVariables,getColor,getSpacing};export default dattaAbleTheme;", "map": {"version": 3, "names": ["colors", "primary", "main", "light", "dark", "contrastText", "secondary", "success", "warning", "error", "info", "background", "default", "paper", "text", "disabled", "divider", "border", "typography", "fontFamily", "fontSize", "xs", "sm", "base", "lg", "xl", "fontWeight", "normal", "medium", "semibold", "bold", "lineHeight", "tight", "relaxed", "spacing", "borderRadius", "none", "md", "full", "shadows", "inner", "breakpoints", "xxl", "layout", "sidebar", "width", "collapsedWidth", "header", "height", "footer", "components", "card", "boxShadow", "button", "padding", "input", "modal", "getColor", "colorPath", "keys", "split", "result", "key", "getSpacing", "value", "cssVariables", "dattaAbleTheme"], "sources": ["C:/laragon/www/frontend/src/theme/dattaAbleTheme.js"], "sourcesContent": ["// Datta Able Theme Configuration\n// This replaces the Material-UI theme with Bootstrap-based styling\n\n// Color palette based on Datta Able design\nexport const colors = {\n  primary: {\n    main: '#1976d2',\n    light: '#42a5f5',\n    dark: '#1565c0',\n    contrastText: '#ffffff',\n  },\n  secondary: {\n    main: '#dc004e',\n    light: '#ff5983',\n    dark: '#9a0036',\n    contrastText: '#ffffff',\n  },\n  success: {\n    main: '#4caf50',\n    light: '#81c784',\n    dark: '#388e3c',\n    contrastText: '#ffffff',\n  },\n  warning: {\n    main: '#ff9800',\n    light: '#ffb74d',\n    dark: '#f57c00',\n    contrastText: '#ffffff',\n  },\n  error: {\n    main: '#f44336',\n    light: '#e57373',\n    dark: '#d32f2f',\n    contrastText: '#ffffff',\n  },\n  info: {\n    main: '#2196f3',\n    light: '#64b5f6',\n    dark: '#1976d2',\n    contrastText: '#ffffff',\n  },\n  background: {\n    default: '#f8f9fa',\n    paper: '#ffffff',\n    light: '#f5f5f5',\n  },\n  text: {\n    primary: '#212529',\n    secondary: '#6c757d',\n    disabled: '#adb5bd',\n  },\n  divider: '#dee2e6',\n  border: '#e9ecef',\n};\n\n// Typography configuration\nexport const typography = {\n  fontFamily: '\"Open Sans\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif',\n  fontSize: {\n    xs: '0.75rem',    // 12px\n    sm: '0.875rem',   // 14px\n    base: '1rem',     // 16px\n    lg: '1.125rem',   // 18px\n    xl: '1.25rem',    // 20px\n    '2xl': '1.5rem',  // 24px\n    '3xl': '1.875rem', // 30px\n    '4xl': '2.25rem', // 36px\n    '5xl': '3rem',    // 48px\n  },\n  fontWeight: {\n    light: 300,\n    normal: 400,\n    medium: 500,\n    semibold: 600,\n    bold: 700,\n  },\n  lineHeight: {\n    tight: 1.25,\n    normal: 1.5,\n    relaxed: 1.75,\n  },\n};\n\n// Spacing configuration (based on Bootstrap's spacing scale)\nexport const spacing = {\n  0: '0',\n  1: '0.25rem',  // 4px\n  2: '0.5rem',   // 8px\n  3: '0.75rem',  // 12px\n  4: '1rem',     // 16px\n  5: '1.25rem',  // 20px\n  6: '1.5rem',   // 24px\n  8: '2rem',     // 32px\n  10: '2.5rem',  // 40px\n  12: '3rem',    // 48px\n  16: '4rem',    // 64px\n  20: '5rem',    // 80px\n  24: '6rem',    // 96px\n};\n\n// Border radius configuration\nexport const borderRadius = {\n  none: '0',\n  sm: '0.125rem',   // 2px\n  base: '0.25rem',  // 4px\n  md: '0.375rem',   // 6px\n  lg: '0.5rem',     // 8px\n  xl: '0.75rem',    // 12px\n  '2xl': '1rem',    // 16px\n  '3xl': '1.5rem',  // 24px\n  full: '9999px',\n};\n\n// Shadow configuration\nexport const shadows = {\n  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',\n  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',\n  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',\n};\n\n// Breakpoints (Bootstrap breakpoints)\nexport const breakpoints = {\n  xs: '0px',\n  sm: '576px',\n  md: '768px',\n  lg: '992px',\n  xl: '1200px',\n  xxl: '1400px',\n};\n\n// Layout configuration\nexport const layout = {\n  sidebar: {\n    width: '260px',\n    collapsedWidth: '70px',\n  },\n  header: {\n    height: '70px',\n  },\n  footer: {\n    height: '60px',\n  },\n};\n\n// Component styles\nexport const components = {\n  card: {\n    borderRadius: borderRadius.lg,\n    boxShadow: shadows.sm,\n    border: `1px solid ${colors.border}`,\n  },\n  button: {\n    borderRadius: borderRadius.md,\n    fontWeight: typography.fontWeight.medium,\n    padding: {\n      sm: `${spacing[2]} ${spacing[3]}`,\n      md: `${spacing[3]} ${spacing[4]}`,\n      lg: `${spacing[4]} ${spacing[6]}`,\n    },\n  },\n  input: {\n    borderRadius: borderRadius.md,\n    border: `1px solid ${colors.border}`,\n    padding: `${spacing[3]} ${spacing[4]}`,\n  },\n  modal: {\n    borderRadius: borderRadius.lg,\n    boxShadow: shadows.xl,\n  },\n};\n\n// Utility functions\nexport const getColor = (colorPath) => {\n  const keys = colorPath.split('.');\n  let result = colors;\n  for (const key of keys) {\n    result = result[key];\n    if (!result) return colors.primary.main; // fallback\n  }\n  return result;\n};\n\nexport const getSpacing = (value) => {\n  if (typeof value === 'number') {\n    return spacing[value] || `${value * 0.25}rem`;\n  }\n  return spacing[value] || value;\n};\n\n// CSS custom properties for dynamic theming\nexport const cssVariables = {\n  '--color-primary': colors.primary.main,\n  '--color-primary-light': colors.primary.light,\n  '--color-primary-dark': colors.primary.dark,\n  '--color-secondary': colors.secondary.main,\n  '--color-success': colors.success.main,\n  '--color-warning': colors.warning.main,\n  '--color-error': colors.error.main,\n  '--color-info': colors.info.main,\n  '--color-background': colors.background.default,\n  '--color-paper': colors.background.paper,\n  '--color-text-primary': colors.text.primary,\n  '--color-text-secondary': colors.text.secondary,\n  '--color-border': colors.border,\n  '--font-family': typography.fontFamily,\n  '--sidebar-width': layout.sidebar.width,\n  '--header-height': layout.header.height,\n  '--border-radius': borderRadius.md,\n  '--shadow-sm': shadows.sm,\n  '--shadow-md': shadows.md,\n  '--shadow-lg': shadows.lg,\n};\n\n// Export default theme object\nconst dattaAbleTheme = {\n  colors,\n  typography,\n  spacing,\n  borderRadius,\n  shadows,\n  breakpoints,\n  layout,\n  components,\n  cssVariables,\n  getColor,\n  getSpacing,\n};\n\nexport default dattaAbleTheme;\n"], "mappings": "AAAA;AACA;AAEA;AACA,MAAO,MAAM,CAAAA,MAAM,CAAG,CACpBC,OAAO,CAAE,CACPC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SAAS,CACfC,YAAY,CAAE,SAChB,CAAC,CACDC,SAAS,CAAE,CACTJ,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SAAS,CACfC,YAAY,CAAE,SAChB,CAAC,CACDE,OAAO,CAAE,CACPL,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SAAS,CACfC,YAAY,CAAE,SAChB,CAAC,CACDG,OAAO,CAAE,CACPN,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SAAS,CACfC,YAAY,CAAE,SAChB,CAAC,CACDI,KAAK,CAAE,CACLP,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SAAS,CACfC,YAAY,CAAE,SAChB,CAAC,CACDK,IAAI,CAAE,CACJR,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SAAS,CACfC,YAAY,CAAE,SAChB,CAAC,CACDM,UAAU,CAAE,CACVC,OAAO,CAAE,SAAS,CAClBC,KAAK,CAAE,SAAS,CAChBV,KAAK,CAAE,SACT,CAAC,CACDW,IAAI,CAAE,CACJb,OAAO,CAAE,SAAS,CAClBK,SAAS,CAAE,SAAS,CACpBS,QAAQ,CAAE,SACZ,CAAC,CACDC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,SACV,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,UAAU,CAAG,CACxBC,UAAU,CAAE,yGAAyG,CACrHC,QAAQ,CAAE,CACRC,EAAE,CAAE,SAAS,CAAK;AAClBC,EAAE,CAAE,UAAU,CAAI;AAClBC,IAAI,CAAE,MAAM,CAAM;AAClBC,EAAE,CAAE,UAAU,CAAI;AAClBC,EAAE,CAAE,SAAS,CAAK;AAClB,KAAK,CAAE,QAAQ,CAAG;AAClB,KAAK,CAAE,UAAU,CAAE;AACnB,KAAK,CAAE,SAAS,CAAE;AAClB,KAAK,CAAE,MAAW;AACpB,CAAC,CACDC,UAAU,CAAE,CACVvB,KAAK,CAAE,GAAG,CACVwB,MAAM,CAAE,GAAG,CACXC,MAAM,CAAE,GAAG,CACXC,QAAQ,CAAE,GAAG,CACbC,IAAI,CAAE,GACR,CAAC,CACDC,UAAU,CAAE,CACVC,KAAK,CAAE,IAAI,CACXL,MAAM,CAAE,GAAG,CACXM,OAAO,CAAE,IACX,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,OAAO,CAAG,CACrB,CAAC,CAAE,GAAG,CACN,CAAC,CAAE,SAAS,CAAG;AACf,CAAC,CAAE,QAAQ,CAAI;AACf,CAAC,CAAE,SAAS,CAAG;AACf,CAAC,CAAE,MAAM,CAAM;AACf,CAAC,CAAE,SAAS,CAAG;AACf,CAAC,CAAE,QAAQ,CAAI;AACf,CAAC,CAAE,MAAM,CAAM;AACf,EAAE,CAAE,QAAQ,CAAG;AACf,EAAE,CAAE,MAAM,CAAK;AACf,EAAE,CAAE,MAAM,CAAK;AACf,EAAE,CAAE,MAAM,CAAK;AACf,EAAE,CAAE,MAAW;AACjB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,YAAY,CAAG,CAC1BC,IAAI,CAAE,GAAG,CACTd,EAAE,CAAE,UAAU,CAAI;AAClBC,IAAI,CAAE,SAAS,CAAG;AAClBc,EAAE,CAAE,UAAU,CAAI;AAClBb,EAAE,CAAE,QAAQ,CAAM;AAClBC,EAAE,CAAE,SAAS,CAAK;AAClB,KAAK,CAAE,MAAM,CAAK;AAClB,KAAK,CAAE,QAAQ,CAAG;AAClBa,IAAI,CAAE,QACR,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,OAAO,CAAG,CACrBjB,EAAE,CAAE,iCAAiC,CACrCC,IAAI,CAAE,iEAAiE,CACvEc,EAAE,CAAE,uEAAuE,CAC3Eb,EAAE,CAAE,yEAAyE,CAC7EC,EAAE,CAAE,2EAA2E,CAC/E,KAAK,CAAE,uCAAuC,CAC9Ce,KAAK,CAAE,uCACT,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,WAAW,CAAG,CACzBpB,EAAE,CAAE,KAAK,CACTC,EAAE,CAAE,OAAO,CACXe,EAAE,CAAE,OAAO,CACXb,EAAE,CAAE,OAAO,CACXC,EAAE,CAAE,QAAQ,CACZiB,GAAG,CAAE,QACP,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,MAAM,CAAG,CACpBC,OAAO,CAAE,CACPC,KAAK,CAAE,OAAO,CACdC,cAAc,CAAE,MAClB,CAAC,CACDC,MAAM,CAAE,CACNC,MAAM,CAAE,MACV,CAAC,CACDC,MAAM,CAAE,CACND,MAAM,CAAE,MACV,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAE,UAAU,CAAG,CACxBC,IAAI,CAAE,CACJhB,YAAY,CAAEA,YAAY,CAACX,EAAE,CAC7B4B,SAAS,CAAEb,OAAO,CAACjB,EAAE,CACrBL,MAAM,CAAE,aAAajB,MAAM,CAACiB,MAAM,EACpC,CAAC,CACDoC,MAAM,CAAE,CACNlB,YAAY,CAAEA,YAAY,CAACE,EAAE,CAC7BX,UAAU,CAAER,UAAU,CAACQ,UAAU,CAACE,MAAM,CACxC0B,OAAO,CAAE,CACPhC,EAAE,CAAE,GAAGY,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,EAAE,CACjCG,EAAE,CAAE,GAAGH,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,EAAE,CACjCV,EAAE,CAAE,GAAGU,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,EACjC,CACF,CAAC,CACDqB,KAAK,CAAE,CACLpB,YAAY,CAAEA,YAAY,CAACE,EAAE,CAC7BpB,MAAM,CAAE,aAAajB,MAAM,CAACiB,MAAM,EAAE,CACpCqC,OAAO,CAAE,GAAGpB,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,EACtC,CAAC,CACDsB,KAAK,CAAE,CACLrB,YAAY,CAAEA,YAAY,CAACX,EAAE,CAC7B4B,SAAS,CAAEb,OAAO,CAACd,EACrB,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAgC,QAAQ,CAAIC,SAAS,EAAK,CACrC,KAAM,CAAAC,IAAI,CAAGD,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC,CACjC,GAAI,CAAAC,MAAM,CAAG7D,MAAM,CACnB,IAAK,KAAM,CAAA8D,GAAG,GAAI,CAAAH,IAAI,CAAE,CACtBE,MAAM,CAAGA,MAAM,CAACC,GAAG,CAAC,CACpB,GAAI,CAACD,MAAM,CAAE,MAAO,CAAA7D,MAAM,CAACC,OAAO,CAACC,IAAI,CAAE;AAC3C,CACA,MAAO,CAAA2D,MAAM,CACf,CAAC,CAED,MAAO,MAAM,CAAAE,UAAU,CAAIC,KAAK,EAAK,CACnC,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC7B,MAAO,CAAA9B,OAAO,CAAC8B,KAAK,CAAC,EAAI,GAAGA,KAAK,CAAG,IAAI,KAAK,CAC/C,CACA,MAAO,CAAA9B,OAAO,CAAC8B,KAAK,CAAC,EAAIA,KAAK,CAChC,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,YAAY,CAAG,CAC1B,iBAAiB,CAAEjE,MAAM,CAACC,OAAO,CAACC,IAAI,CACtC,uBAAuB,CAAEF,MAAM,CAACC,OAAO,CAACE,KAAK,CAC7C,sBAAsB,CAAEH,MAAM,CAACC,OAAO,CAACG,IAAI,CAC3C,mBAAmB,CAAEJ,MAAM,CAACM,SAAS,CAACJ,IAAI,CAC1C,iBAAiB,CAAEF,MAAM,CAACO,OAAO,CAACL,IAAI,CACtC,iBAAiB,CAAEF,MAAM,CAACQ,OAAO,CAACN,IAAI,CACtC,eAAe,CAAEF,MAAM,CAACS,KAAK,CAACP,IAAI,CAClC,cAAc,CAAEF,MAAM,CAACU,IAAI,CAACR,IAAI,CAChC,oBAAoB,CAAEF,MAAM,CAACW,UAAU,CAACC,OAAO,CAC/C,eAAe,CAAEZ,MAAM,CAACW,UAAU,CAACE,KAAK,CACxC,sBAAsB,CAAEb,MAAM,CAACc,IAAI,CAACb,OAAO,CAC3C,wBAAwB,CAAED,MAAM,CAACc,IAAI,CAACR,SAAS,CAC/C,gBAAgB,CAAEN,MAAM,CAACiB,MAAM,CAC/B,eAAe,CAAEC,UAAU,CAACC,UAAU,CACtC,iBAAiB,CAAEwB,MAAM,CAACC,OAAO,CAACC,KAAK,CACvC,iBAAiB,CAAEF,MAAM,CAACI,MAAM,CAACC,MAAM,CACvC,iBAAiB,CAAEb,YAAY,CAACE,EAAE,CAClC,aAAa,CAAEE,OAAO,CAACjB,EAAE,CACzB,aAAa,CAAEiB,OAAO,CAACF,EAAE,CACzB,aAAa,CAAEE,OAAO,CAACf,EACzB,CAAC,CAED;AACA,KAAM,CAAA0C,cAAc,CAAG,CACrBlE,MAAM,CACNkB,UAAU,CACVgB,OAAO,CACPC,YAAY,CACZI,OAAO,CACPE,WAAW,CACXE,MAAM,CACNO,UAAU,CACVe,YAAY,CACZR,QAAQ,CACRM,UACF,CAAC,CAED,cAAe,CAAAG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}