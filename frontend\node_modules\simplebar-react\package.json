{"name": "simplebar-react", "version": "3.3.2", "description": "React component for SimpleBar", "files": ["dist", "README.md"], "author": "<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/grsmto/simplebar.git", "directory": "packages/simplebar-react"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}, "./dist/simplebar.min.css": "./dist/simplebar.min.css"}, "sideEffects": ["*.css"], "bugs": "https://github.com/grsmto/simplebar/issues", "homepage": "https://grsmto.github.io/simplebar/", "license": "MIT", "scripts": {"build": "rollup -c && minify ../simplebar-core/src/simplebar.css > dist/simplebar.min.css", "dev": "rollup -c -w", "test": "jest", "version": "yarn build", "precommit": "lint-staged"}, "dependencies": {"simplebar-core": "^1.3.2"}, "peerDependencies": {"react": ">=16.8.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "eslint-config-react-app": "^5.0.2", "eslint-plugin-react": "^7.14.3"}, "lint-staged": {"*.{js,jsx,json}": ["prettier-eslint --write", "git add"]}, "gitHead": "46a2c4a8f5b0a4ed66f80dcd4d2a74ee52ce1ff3"}