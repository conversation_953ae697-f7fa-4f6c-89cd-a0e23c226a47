import React, { useState, useEffect } from 'react';
import {
  Container,
  Row,
  Col,
  Card,
  Nav,
  Tab,
  Toast,
  ToastContainer,
} from 'react-bootstrap';
import WalletBalance from '../../components/wallet/WalletBalance';
import WalletTopUp from '../../components/wallet/WalletTopUp';
import WalletTransactionHistory from '../../components/wallet/WalletTransactionHistory';
import creditService, { CreditStatistics } from '../../services/creditService';
import dattaAbleTheme from '../../theme/dattaAbleTheme';

// Notification state interface

interface NotificationState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
}

const Wallet: React.FC = () => {
  const [activeTab, setActiveTab] = useState('topup');
  const [statistics, setStatistics] = useState<CreditStatistics | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [notification, setNotification] = useState<NotificationState>({
    open: false,
    message: '',
    severity: 'info',
  });


  const handleTabChange = (eventKey: string | null) => {
    if (eventKey) setActiveTab(eventKey);
  };

  // Fetch wallet statistics
  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        const data = await creditService.getStatistics();
        setStatistics(data);
      } catch (error) {
        console.error('Failed to fetch wallet statistics:', error);
      }
    };
    fetchStatistics();
  }, [refreshTrigger]);

  // Check for payment status in URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const billplzId = urlParams.get('billplz[id]');
    const billplzPaid = urlParams.get('billplz[paid]');
    const billplzState = urlParams.get('billplz[state]');

    if (billplzId && billplzPaid && billplzState) {
      if (billplzPaid === 'true' && billplzState === 'paid') {
        setNotification({
          open: true,
          message: 'Payment successful! Your wallet has been topped up.',
          severity: 'success',
        });
        setRefreshTrigger(prev => prev + 1);
      } else if (billplzPaid === 'false') {
        setNotification({
          open: true,
          message: 'Payment was not completed. Please try again.',
          severity: 'warning',
        });
      }

      // Clean up URL parameters
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, []);

  const handleTopUpSuccess = () => {
    setRefreshTrigger(prev => prev + 1);
    setNotification({
      open: true,
      message: 'Top up initiated! You will be redirected to complete payment.',
      severity: 'info',
    });
  };

  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  const handleTopUpClick = () => {
    setActiveTab('topup'); // Switch to top-up tab
  };

  const handleHistoryClick = () => {
    setActiveTab('history'); // Switch to history tab
  };

  const currentBalance = statistics?.current_balance || 0;

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: dattaAbleTheme.colors.background.default,
      fontFamily: dattaAbleTheme.typography.fontFamily
    }}>
      <Container fluid>
        {/* Enhanced Header */}
        <Row className="mb-4">
          <Col>
            <div className="d-flex flex-column flex-sm-row align-items-center align-items-sm-start justify-content-between gap-3 mb-3">
              <div className="text-center text-sm-start">
                <h1
                  className="display-4 fw-bold mb-2"
                  style={{
                    background: `linear-gradient(135deg, ${dattaAbleTheme.colors.primary.main} 0%, ${dattaAbleTheme.colors.primary.dark} 100%)`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                  }}
                >
                  <span
                    className="d-inline-flex align-items-center justify-content-center me-3"
                    style={{
                      width: '60px',
                      height: '60px',
                      borderRadius: dattaAbleTheme.borderRadius['2xl'],
                      background: `linear-gradient(135deg, ${dattaAbleTheme.colors.primary.main} 0%, ${dattaAbleTheme.colors.primary.dark} 100%)`,
                      color: 'white',
                      boxShadow: dattaAbleTheme.shadows.lg,
                    }}
                  >
                    <i className="fas fa-wallet"></i>
                  </span>
                  Wallet Management
                </h1>
                <h6
                  className="text-muted mb-0"
                  style={{
                    fontWeight: dattaAbleTheme.typography.fontWeight.normal,
                    maxWidth: '600px'
                  }}
                >
                  Manage your Malaysian Ringgit (RM) wallet balance, top up funds, and track all your transactions
                </h6>
              </div>

              {/* Quick Stats */}
              <div className="d-flex flex-row flex-sm-column gap-2 align-items-center">
                <small className="text-muted">Current Balance</small>
                <h5
                  className="mb-0 fw-bold"
                  style={{ color: dattaAbleTheme.colors.primary.main }}
                >
                  {statistics ? creditService.formatWalletBalance(statistics.current_balance) : '---'}
                </h5>
              </div>
            </div>
          </Col>
        </Row>

        {/* Wallet Balance Overview */}
        <Row className="mb-4">
          <Col>
            <WalletBalance
              refreshTrigger={refreshTrigger}
              onTopUpClick={handleTopUpClick}
              onHistoryClick={handleHistoryClick}
            />
          </Col>
        </Row>

        {/* Enhanced Main Content Tabs */}
        <Row>
          <Col>
            <Card
              className="border-0 shadow-sm"
              style={{
                borderRadius: dattaAbleTheme.borderRadius.lg,
                overflow: 'hidden'
              }}
            >
              <Card.Header
                className="bg-light border-0"
                style={{ padding: 0 }}
              >
                <Tab.Container
                  activeKey={activeTab}
                  onSelect={handleTabChange}
                >
                  <Nav
                    variant="tabs"
                    className="px-3"
                    style={{
                      borderBottom: 'none',
                    }}
                  >
                    <Nav.Item>
                      <Nav.Link
                        eventKey="topup"
                        className="d-flex align-items-center gap-2 py-3"
                        style={{
                          fontSize: dattaAbleTheme.typography.fontSize.sm,
                          fontWeight: dattaAbleTheme.typography.fontWeight.semibold,
                          borderRadius: `${dattaAbleTheme.borderRadius.lg} ${dattaAbleTheme.borderRadius.lg} 0 0`,
                          border: 'none',
                        }}
                      >
                        <i className="fas fa-plus"></i>
                        Top Up Wallet
                      </Nav.Link>
                    </Nav.Item>
                    <Nav.Item>
                      <Nav.Link
                        eventKey="history"
                        className="d-flex align-items-center gap-2 py-3"
                        style={{
                          fontSize: dattaAbleTheme.typography.fontSize.sm,
                          fontWeight: dattaAbleTheme.typography.fontWeight.semibold,
                          borderRadius: `${dattaAbleTheme.borderRadius.lg} ${dattaAbleTheme.borderRadius.lg} 0 0`,
                          border: 'none',
                        }}
                      >
                        <i className="fas fa-history"></i>
                        Transaction History
                      </Nav.Link>
                    </Nav.Item>
                  </Nav>

                  <Tab.Content>
                    <Tab.Pane eventKey="topup">
                      <Card.Body className="p-4">
                        <WalletTopUp
                          onTopUpSuccess={handleTopUpSuccess}
                          currentBalance={currentBalance}
                        />
                      </Card.Body>
                    </Tab.Pane>

                    <Tab.Pane eventKey="history">
                      <Card.Body className="p-4">
                        <WalletTransactionHistory refreshTrigger={refreshTrigger} />
                      </Card.Body>
                    </Tab.Pane>
                  </Tab.Content>
                </Tab.Container>
              </Card.Header>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Notification Toast */}
      <ToastContainer position="bottom-center" className="p-3">
        <Toast
          show={notification.open}
          onClose={handleCloseNotification}
          delay={6000}
          autohide
          bg={notification.severity === 'error' ? 'danger' :
              notification.severity === 'warning' ? 'warning' :
              notification.severity === 'success' ? 'success' : 'info'}
        >
          <Toast.Header closeButton>
            <strong className="me-auto text-white">
              {notification.severity === 'error' ? 'Error' :
               notification.severity === 'warning' ? 'Warning' :
               notification.severity === 'success' ? 'Success' : 'Info'}
            </strong>
          </Toast.Header>
          <Toast.Body className="text-white">
            {notification.message}
          </Toast.Body>
        </Toast>
      </ToastContainer>
    </div>
  );
};

export default Wallet;
