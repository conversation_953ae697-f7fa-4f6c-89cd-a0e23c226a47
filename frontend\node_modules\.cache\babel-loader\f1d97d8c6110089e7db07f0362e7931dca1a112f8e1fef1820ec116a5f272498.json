{"ast": null, "code": "import React from'react';import{Breadcrumb}from'react-bootstrap';import{useLocation,Link}from'react-router-dom';import dattaAbleTheme from'../../theme/dattaAbleTheme';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DattaAbleBreadcrumbs=()=>{const location=useLocation();// Define breadcrumb mappings\nconst breadcrumbMap={'/dashboard':'Dashboard','/dashboard/wallet':'Wallet','/dashboard/order':'Order','/dashboard/orders':'My Orders'};// Generate breadcrumb items\nconst generateBreadcrumbs=()=>{const pathSegments=location.pathname.split('/').filter(segment=>segment);const breadcrumbs=[];// Always start with Dashboard\nbreadcrumbs.push({path:'/dashboard',label:'Dashboard',isActive:location.pathname==='/dashboard'});// Add current page if not dashboard\nif(location.pathname!=='/dashboard'){const currentLabel=breadcrumbMap[location.pathname]||'Page';breadcrumbs.push({path:location.pathname,label:currentLabel,isActive:true});}return breadcrumbs;};const breadcrumbs=generateBreadcrumbs();const breadcrumbStyles={backgroundColor:'transparent',padding:`0 0 ${dattaAbleTheme.spacing[4]}`,margin:0,fontSize:dattaAbleTheme.typography.fontSize.sm};const breadcrumbItemStyles={color:dattaAbleTheme.colors.text.secondary};const activeBreadcrumbStyles={color:dattaAbleTheme.colors.text.primary,fontWeight:dattaAbleTheme.typography.fontWeight.medium};const linkStyles={color:dattaAbleTheme.colors.primary.main,textDecoration:'none'};// Don't show breadcrumbs if only one item (Dashboard)\nif(breadcrumbs.length<=1){return null;}return/*#__PURE__*/_jsxs(Breadcrumb,{style:breadcrumbStyles,children:[breadcrumbs.map((crumb,index)=>/*#__PURE__*/_jsx(Breadcrumb.Item,{active:crumb.isActive,style:crumb.isActive?activeBreadcrumbStyles:breadcrumbItemStyles,children:crumb.isActive?crumb.label:/*#__PURE__*/_jsx(Link,{to:crumb.path,style:linkStyles,onMouseEnter:e=>{e.target.style.textDecoration='underline';},onMouseLeave:e=>{e.target.style.textDecoration='none';},children:crumb.label})},crumb.path)),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:`\n        .breadcrumb-item + .breadcrumb-item::before {\n          content: '/';\n          color: ${dattaAbleTheme.colors.text.secondary};\n        }\n\n        .breadcrumb-item.active {\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n      `})]});};export default DattaAbleBreadcrumbs;", "map": {"version": 3, "names": ["React", "Breadcrumb", "useLocation", "Link", "dattaAbleTheme", "jsx", "_jsx", "jsxs", "_jsxs", "DattaAbleBreadcrumbs", "location", "breadcrumbMap", "generateBreadcrumbs", "pathSegments", "pathname", "split", "filter", "segment", "breadcrumbs", "push", "path", "label", "isActive", "current<PERSON><PERSON><PERSON>", "breadcrumbStyles", "backgroundColor", "padding", "spacing", "margin", "fontSize", "typography", "sm", "breadcrumbItemStyles", "color", "colors", "text", "secondary", "activeBreadcrumbStyles", "primary", "fontWeight", "medium", "linkStyles", "main", "textDecoration", "length", "style", "children", "map", "crumb", "index", "<PERSON><PERSON>", "active", "to", "onMouseEnter", "e", "target", "onMouseLeave"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/DattaAbleBreadcrumbs.jsx"], "sourcesContent": ["import React from 'react';\nimport { Breadcrumb } from 'react-bootstrap';\nimport { useLocation, Link } from 'react-router-dom';\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\n\nconst DattaAbleBreadcrumbs = () => {\n  const location = useLocation();\n  \n  // Define breadcrumb mappings\n  const breadcrumbMap = {\n    '/dashboard': 'Dashboard',\n    '/dashboard/wallet': 'Wallet',\n    '/dashboard/order': 'Order',\n    '/dashboard/orders': 'My Orders',\n  };\n\n  // Generate breadcrumb items\n  const generateBreadcrumbs = () => {\n    const pathSegments = location.pathname.split('/').filter(segment => segment);\n    const breadcrumbs = [];\n    \n    // Always start with Dashboard\n    breadcrumbs.push({\n      path: '/dashboard',\n      label: 'Dashboard',\n      isActive: location.pathname === '/dashboard'\n    });\n\n    // Add current page if not dashboard\n    if (location.pathname !== '/dashboard') {\n      const currentLabel = breadcrumbMap[location.pathname] || 'Page';\n      breadcrumbs.push({\n        path: location.pathname,\n        label: currentLabel,\n        isActive: true\n      });\n    }\n\n    return breadcrumbs;\n  };\n\n  const breadcrumbs = generateBreadcrumbs();\n\n  const breadcrumbStyles = {\n    backgroundColor: 'transparent',\n    padding: `0 0 ${dattaAbleTheme.spacing[4]}`,\n    margin: 0,\n    fontSize: dattaAbleTheme.typography.fontSize.sm,\n  };\n\n  const breadcrumbItemStyles = {\n    color: dattaAbleTheme.colors.text.secondary,\n  };\n\n  const activeBreadcrumbStyles = {\n    color: dattaAbleTheme.colors.text.primary,\n    fontWeight: dattaAbleTheme.typography.fontWeight.medium,\n  };\n\n  const linkStyles = {\n    color: dattaAbleTheme.colors.primary.main,\n    textDecoration: 'none',\n  };\n\n  // Don't show breadcrumbs if only one item (Dashboard)\n  if (breadcrumbs.length <= 1) {\n    return null;\n  }\n\n  return (\n    <Breadcrumb style={breadcrumbStyles}>\n      {breadcrumbs.map((crumb, index) => (\n        <Breadcrumb.Item\n          key={crumb.path}\n          active={crumb.isActive}\n          style={crumb.isActive ? activeBreadcrumbStyles : breadcrumbItemStyles}\n        >\n          {crumb.isActive ? (\n            crumb.label\n          ) : (\n            <Link\n              to={crumb.path}\n              style={linkStyles}\n              onMouseEnter={(e) => {\n                e.target.style.textDecoration = 'underline';\n              }}\n              onMouseLeave={(e) => {\n                e.target.style.textDecoration = 'none';\n              }}\n            >\n              {crumb.label}\n            </Link>\n          )}\n        </Breadcrumb.Item>\n      ))}\n\n      <style jsx>{`\n        .breadcrumb-item + .breadcrumb-item::before {\n          content: '/';\n          color: ${dattaAbleTheme.colors.text.secondary};\n        }\n\n        .breadcrumb-item.active {\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n      `}</style>\n    </Breadcrumb>\n  );\n};\n\nexport default DattaAbleBreadcrumbs;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,UAAU,KAAQ,iBAAiB,CAC5C,OAASC,WAAW,CAAEC,IAAI,KAAQ,kBAAkB,CACpD,MAAO,CAAAC,cAAc,KAAM,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,QAAQ,CAAGR,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAS,aAAa,CAAG,CACpB,YAAY,CAAE,WAAW,CACzB,mBAAmB,CAAE,QAAQ,CAC7B,kBAAkB,CAAE,OAAO,CAC3B,mBAAmB,CAAE,WACvB,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,YAAY,CAAGH,QAAQ,CAACI,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,EAAIA,OAAO,CAAC,CAC5E,KAAM,CAAAC,WAAW,CAAG,EAAE,CAEtB;AACAA,WAAW,CAACC,IAAI,CAAC,CACfC,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,WAAW,CAClBC,QAAQ,CAAEZ,QAAQ,CAACI,QAAQ,GAAK,YAClC,CAAC,CAAC,CAEF;AACA,GAAIJ,QAAQ,CAACI,QAAQ,GAAK,YAAY,CAAE,CACtC,KAAM,CAAAS,YAAY,CAAGZ,aAAa,CAACD,QAAQ,CAACI,QAAQ,CAAC,EAAI,MAAM,CAC/DI,WAAW,CAACC,IAAI,CAAC,CACfC,IAAI,CAAEV,QAAQ,CAACI,QAAQ,CACvBO,KAAK,CAAEE,YAAY,CACnBD,QAAQ,CAAE,IACZ,CAAC,CAAC,CACJ,CAEA,MAAO,CAAAJ,WAAW,CACpB,CAAC,CAED,KAAM,CAAAA,WAAW,CAAGN,mBAAmB,CAAC,CAAC,CAEzC,KAAM,CAAAY,gBAAgB,CAAG,CACvBC,eAAe,CAAE,aAAa,CAC9BC,OAAO,CAAE,OAAOtB,cAAc,CAACuB,OAAO,CAAC,CAAC,CAAC,EAAE,CAC3CC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAEzB,cAAc,CAAC0B,UAAU,CAACD,QAAQ,CAACE,EAC/C,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAG,CAC3BC,KAAK,CAAE7B,cAAc,CAAC8B,MAAM,CAACC,IAAI,CAACC,SACpC,CAAC,CAED,KAAM,CAAAC,sBAAsB,CAAG,CAC7BJ,KAAK,CAAE7B,cAAc,CAAC8B,MAAM,CAACC,IAAI,CAACG,OAAO,CACzCC,UAAU,CAAEnC,cAAc,CAAC0B,UAAU,CAACS,UAAU,CAACC,MACnD,CAAC,CAED,KAAM,CAAAC,UAAU,CAAG,CACjBR,KAAK,CAAE7B,cAAc,CAAC8B,MAAM,CAACI,OAAO,CAACI,IAAI,CACzCC,cAAc,CAAE,MAClB,CAAC,CAED;AACA,GAAIzB,WAAW,CAAC0B,MAAM,EAAI,CAAC,CAAE,CAC3B,MAAO,KAAI,CACb,CAEA,mBACEpC,KAAA,CAACP,UAAU,EAAC4C,KAAK,CAAErB,gBAAiB,CAAAsB,QAAA,EACjC5B,WAAW,CAAC6B,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,gBAC5B3C,IAAA,CAACL,UAAU,CAACiD,IAAI,EAEdC,MAAM,CAAEH,KAAK,CAAC1B,QAAS,CACvBuB,KAAK,CAAEG,KAAK,CAAC1B,QAAQ,CAAGe,sBAAsB,CAAGL,oBAAqB,CAAAc,QAAA,CAErEE,KAAK,CAAC1B,QAAQ,CACb0B,KAAK,CAAC3B,KAAK,cAEXf,IAAA,CAACH,IAAI,EACHiD,EAAE,CAAEJ,KAAK,CAAC5B,IAAK,CACfyB,KAAK,CAAEJ,UAAW,CAClBY,YAAY,CAAGC,CAAC,EAAK,CACnBA,CAAC,CAACC,MAAM,CAACV,KAAK,CAACF,cAAc,CAAG,WAAW,CAC7C,CAAE,CACFa,YAAY,CAAGF,CAAC,EAAK,CACnBA,CAAC,CAACC,MAAM,CAACV,KAAK,CAACF,cAAc,CAAG,MAAM,CACxC,CAAE,CAAAG,QAAA,CAEDE,KAAK,CAAC3B,KAAK,CACR,CACP,EAnBI2B,KAAK,CAAC5B,IAoBI,CAClB,CAAC,cAEFd,IAAA,UAAOD,GAAG,MAAAyC,QAAA,CAAE;AAClB;AACA;AACA,mBAAmB1C,cAAc,CAAC8B,MAAM,CAACC,IAAI,CAACC,SAAS;AACvD;AACA;AACA;AACA,mBAAmBhC,cAAc,CAAC8B,MAAM,CAACC,IAAI,CAACG,OAAO;AACrD;AACA,OAAO,CAAQ,CAAC,EACA,CAAC,CAEjB,CAAC,CAED,cAAe,CAAA7B,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}