import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import dattaAbleTheme from '../../theme/dattaAbleTheme';

const DattaAbleFooter = () => {
  const footerStyles = {
    backgroundColor: dattaAbleTheme.colors.background.paper,
    borderTop: `1px solid ${dattaAbleTheme.colors.border}`,
    padding: `${dattaAbleTheme.spacing[4]} 0`,
    marginTop: 'auto',
  };

  const textStyles = {
    fontSize: dattaAbleTheme.typography.fontSize.sm,
    color: dattaAbleTheme.colors.text.secondary,
    margin: 0,
  };

  const linkStyles = {
    color: dattaAbleTheme.colors.primary.main,
    textDecoration: 'none',
    fontWeight: dattaAbleTheme.typography.fontWeight.medium,
  };

  return (
    <footer style={footerStyles}>
      <Container fluid>
        <Row className="align-items-center">
          <Col md={6}>
            <p style={textStyles}>
              © {new Date().getFullYear()} Made with{' '}
              <i className="fas fa-heart text-danger"></i> by{' '}
              <a
                href="#"
                style={linkStyles}
                onMouseEnter={(e) => {
                  e.target.style.textDecoration = 'underline';
                }}
                onMouseLeave={(e) => {
                  e.target.style.textDecoration = 'none';
                }}
              >
                Your Company
              </a>
            </p>
          </Col>
          <Col md={6} className="text-md-end">
            <p style={textStyles}>
              <a
                href="#"
                style={linkStyles}
                className="me-3"
                onMouseEnter={(e) => {
                  e.target.style.textDecoration = 'underline';
                }}
                onMouseLeave={(e) => {
                  e.target.style.textDecoration = 'none';
                }}
              >
                About
              </a>
              <a
                href="#"
                style={linkStyles}
                className="me-3"
                onMouseEnter={(e) => {
                  e.target.style.textDecoration = 'underline';
                }}
                onMouseLeave={(e) => {
                  e.target.style.textDecoration = 'none';
                }}
              >
                Support
              </a>
              <a
                href="#"
                style={linkStyles}
                onMouseEnter={(e) => {
                  e.target.style.textDecoration = 'underline';
                }}
                onMouseLeave={(e) => {
                  e.target.style.textDecoration = 'none';
                }}
              >
                Contact
              </a>
            </p>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default DattaAbleFooter;
