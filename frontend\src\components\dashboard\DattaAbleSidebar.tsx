import React from 'react';
import { Nav } from 'react-bootstrap';
import { useNavigate, useLocation } from 'react-router-dom';
import dattaAbleTheme from '../../theme/dattaAbleTheme';

const DattaAbleSidebar = ({ isOpen, isCollapsed, onToggle, onCollapse }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      text: 'Dashboard',
      icon: 'fas fa-tachometer-alt',
      path: '/dashboard',
    },
    {
      text: 'Order',
      icon: 'fas fa-print',
      path: '/dashboard/order',
    },
    {
      text: 'My Orders',
      icon: 'fas fa-shopping-cart',
      path: '/dashboard/orders',
    },
    {
      text: 'Wallet',
      icon: 'fas fa-wallet',
      path: '/dashboard/wallet',
    },
  ];

  const sidebarStyles = {
    position: 'fixed',
    top: 0,
    left: isOpen || window.innerWidth >= 768 ? 0 : `-${dattaAbleTheme.layout.sidebar.width}`,
    width: isCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width,
    height: '100vh',
    backgroundColor: dattaAbleTheme.colors.background.paper,
    borderRight: `1px solid ${dattaAbleTheme.colors.border}`,
    boxShadow: dattaAbleTheme.shadows.md,
    zIndex: 1050,
    transition: 'all 0.3s ease',
    overflowY: 'auto',
    overflowX: 'hidden',
  };

  const logoStyles = {
    padding: dattaAbleTheme.spacing[4],
    borderBottom: `1px solid ${dattaAbleTheme.colors.border}`,
    textAlign: isCollapsed ? 'center' : 'left',
    height: dattaAbleTheme.layout.header.height,
    display: 'flex',
    alignItems: 'center',
    justifyContent: isCollapsed ? 'center' : 'flex-start',
  };

  const logoTextStyles = {
    fontSize: dattaAbleTheme.typography.fontSize.xl,
    fontWeight: dattaAbleTheme.typography.fontWeight.bold,
    color: dattaAbleTheme.colors.primary.main,
    textDecoration: 'none',
    display: isCollapsed ? 'none' : 'block',
  };

  const logoIconStyles = {
    fontSize: dattaAbleTheme.typography.fontSize['2xl'],
    color: dattaAbleTheme.colors.primary.main,
    marginRight: isCollapsed ? 0 : dattaAbleTheme.spacing[2],
  };

  const navStyles = {
    padding: `${dattaAbleTheme.spacing[4]} 0`,
  };

  const navItemStyles = {
    margin: `0 ${dattaAbleTheme.spacing[3]} ${dattaAbleTheme.spacing[2]}`,
  };

  const navLinkStyles = (isActive) => ({
    display: 'flex',
    alignItems: 'center',
    padding: `${dattaAbleTheme.spacing[3]} ${dattaAbleTheme.spacing[4]}`,
    color: isActive ? dattaAbleTheme.colors.primary.main : dattaAbleTheme.colors.text.primary,
    backgroundColor: isActive ? `${dattaAbleTheme.colors.primary.main}15` : 'transparent',
    borderRadius: dattaAbleTheme.borderRadius.lg,
    textDecoration: 'none',
    fontSize: dattaAbleTheme.typography.fontSize.sm,
    fontWeight: isActive ? dattaAbleTheme.typography.fontWeight.semibold : dattaAbleTheme.typography.fontWeight.normal,
    transition: 'all 0.2s ease',
    cursor: 'pointer',
    border: isActive ? `1px solid ${dattaAbleTheme.colors.primary.main}30` : '1px solid transparent',
  });

  const iconStyles = {
    fontSize: dattaAbleTheme.typography.fontSize.base,
    width: '20px',
    textAlign: 'center',
    marginRight: isCollapsed ? 0 : dattaAbleTheme.spacing[3],
  };

  const textStyles = {
    display: isCollapsed ? 'none' : 'block',
    whiteSpace: 'nowrap',
  };

  const handleNavigation = (path) => {
    navigate(path);
    if (window.innerWidth < 768) {
      onToggle();
    }
  };

  return (
    <div style={sidebarStyles}>
      {/* Logo */}
      <div style={logoStyles}>
        <i className="fas fa-cube" style={logoIconStyles}></i>
        <span style={logoTextStyles}>Datta Able</span>
      </div>

      {/* Navigation */}
      <Nav style={navStyles} className="flex-column">
        {menuItems.map((item, index) => {
          const isActive = location.pathname === item.path;
          
          return (
            <div key={index} style={navItemStyles}>
              <div
                style={navLinkStyles(isActive)}
                onClick={() => handleNavigation(item.path)}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.target.style.backgroundColor = dattaAbleTheme.colors.background.light;
                    e.target.style.color = dattaAbleTheme.colors.primary.main;
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.target.style.backgroundColor = 'transparent';
                    e.target.style.color = dattaAbleTheme.colors.text.primary;
                  }
                }}
              >
                <i className={item.icon} style={iconStyles}></i>
                <span style={textStyles}>{item.text}</span>
              </div>
            </div>
          );
        })}
      </Nav>

      {/* Collapse Toggle (Desktop Only) */}
      {!isCollapsed && window.innerWidth >= 768 && (
        <div
          style={{
            position: 'absolute',
            bottom: dattaAbleTheme.spacing[4],
            left: dattaAbleTheme.spacing[3],
            right: dattaAbleTheme.spacing[3],
          }}
        >
          <div
            style={{
              padding: dattaAbleTheme.spacing[3],
              backgroundColor: dattaAbleTheme.colors.background.light,
              borderRadius: dattaAbleTheme.borderRadius.lg,
              textAlign: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
            }}
            onClick={onCollapse}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = dattaAbleTheme.colors.primary.light + '20';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = dattaAbleTheme.colors.background.light;
            }}
          >
            <i className="fas fa-chevron-left" style={{ color: dattaAbleTheme.colors.text.secondary }}></i>
            <div
              style={{
                fontSize: dattaAbleTheme.typography.fontSize.xs,
                color: dattaAbleTheme.colors.text.secondary,
                marginTop: dattaAbleTheme.spacing[1],
              }}
            >
              Collapse
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        /* Custom scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
          width: 4px;
        }
        
        .sidebar::-webkit-scrollbar-track {
          background: transparent;
        }
        
        .sidebar::-webkit-scrollbar-thumb {
          background: ${dattaAbleTheme.colors.text.secondary}40;
          border-radius: ${dattaAbleTheme.borderRadius.full};
        }
        
        .sidebar::-webkit-scrollbar-thumb:hover {
          background: ${dattaAbleTheme.colors.text.secondary}60;
        }

        /* Mobile responsive */
        @media (max-width: 767.98px) {
          .sidebar-mobile {
            left: ${isOpen ? '0' : `-${dattaAbleTheme.layout.sidebar.width}`} !important;
          }
        }

        /* Tooltip for collapsed sidebar */
        .nav-item-collapsed {
          position: relative;
        }

        .nav-item-collapsed:hover::after {
          content: attr(data-tooltip);
          position: absolute;
          left: 100%;
          top: 50%;
          transform: translateY(-50%);
          background: ${dattaAbleTheme.colors.text.primary};
          color: white;
          padding: ${dattaAbleTheme.spacing[2]} ${dattaAbleTheme.spacing[3]};
          border-radius: ${dattaAbleTheme.borderRadius.md};
          font-size: ${dattaAbleTheme.typography.fontSize.sm};
          white-space: nowrap;
          z-index: 1000;
          margin-left: ${dattaAbleTheme.spacing[2]};
          opacity: ${isCollapsed ? '1' : '0'};
          pointer-events: none;
        }

        .nav-item-collapsed:hover::before {
          content: '';
          position: absolute;
          left: 100%;
          top: 50%;
          transform: translateY(-50%);
          border: 5px solid transparent;
          border-right-color: ${dattaAbleTheme.colors.text.primary};
          margin-left: ${dattaAbleTheme.spacing[1]};
          opacity: ${isCollapsed ? '1' : '0'};
          pointer-events: none;
        }
      `}</style>
    </div>
  );
};

export default DattaAbleSidebar;
