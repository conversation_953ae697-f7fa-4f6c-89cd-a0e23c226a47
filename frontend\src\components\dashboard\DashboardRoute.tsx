import React from 'react';
import DattaAbleLayout from './DattaAbleLayout';
import ProtectedRoute from '../auth/ProtectedRoute';

interface DashboardRouteProps {
  children: React.ReactNode;
}

const DashboardRoute: React.FC<DashboardRouteProps> = ({ children }) => {
  return (
    <ProtectedRoute>
      <DattaAbleLayout>
        {children}
      </DattaAbleLayout>
    </ProtectedRoute>
  );
};

export default DashboardRoute;
