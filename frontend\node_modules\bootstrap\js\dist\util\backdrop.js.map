{"version": 3, "file": "backdrop.js", "sources": ["../../src/util/backdrop.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport {\n  execute, executeAfterTransition, getElement, reflow\n} from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n"], "names": ["NAME", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "EVENT_MOUSEDOWN", "<PERSON><PERSON><PERSON>", "className", "clickCallback", "isAnimated", "isVisible", "rootElement", "DefaultType", "Backdrop", "Config", "constructor", "config", "_config", "_getConfig", "_isAppended", "_element", "show", "callback", "execute", "_append", "element", "_getElement", "reflow", "classList", "add", "_emulateAnimation", "hide", "remove", "dispose", "EventHandler", "off", "backdrop", "document", "createElement", "_configAfterMerge", "getElement", "append", "on", "executeAfterTransition"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAQA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,UAAU,CAAA;EACvB,MAAMC,eAAe,GAAG,MAAM,CAAA;EAC9B,MAAMC,eAAe,GAAG,MAAM,CAAA;EAC9B,MAAMC,eAAe,GAAI,CAAeH,aAAAA,EAAAA,IAAK,CAAC,CAAA,CAAA;EAE9C,MAAMI,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAE,gBAAgB;EAC3BC,EAAAA,aAAa,EAAE,IAAI;EACnBC,EAAAA,UAAU,EAAE,KAAK;EACjBC,EAAAA,SAAS,EAAE,IAAI;EAAE;IACjBC,WAAW,EAAE,MAAM;EACrB,CAAC,CAAA;EAED,MAAMC,WAAW,GAAG;EAClBL,EAAAA,SAAS,EAAE,QAAQ;EACnBC,EAAAA,aAAa,EAAE,iBAAiB;EAChCC,EAAAA,UAAU,EAAE,SAAS;EACrBC,EAAAA,SAAS,EAAE,SAAS;EACpBC,EAAAA,WAAW,EAAE,kBAAA;EACf,CAAC,CAAA;;EAED;EACA;EACA;;EAEA,MAAME,QAAQ,SAASC,MAAM,CAAC;IAC5BC,WAAWA,CAACC,MAAM,EAAE;EAClB,IAAA,KAAK,EAAE,CAAA;MACP,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,UAAU,CAACF,MAAM,CAAC,CAAA;MACtC,IAAI,CAACG,WAAW,GAAG,KAAK,CAAA;MACxB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAA;EACtB,GAAA;;EAEA;IACA,WAAWd,OAAOA,GAAG;EACnB,IAAA,OAAOA,OAAO,CAAA;EAChB,GAAA;IAEA,WAAWM,WAAWA,GAAG;EACvB,IAAA,OAAOA,WAAW,CAAA;EACpB,GAAA;IAEA,WAAWV,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI,CAAA;EACb,GAAA;;EAEA;IACAmB,IAAIA,CAACC,QAAQ,EAAE;EACb,IAAA,IAAI,CAAC,IAAI,CAACL,OAAO,CAACP,SAAS,EAAE;QAC3Ba,gBAAO,CAACD,QAAQ,CAAC,CAAA;EACjB,MAAA,OAAA;EACF,KAAA;MAEA,IAAI,CAACE,OAAO,EAAE,CAAA;EAEd,IAAA,MAAMC,OAAO,GAAG,IAAI,CAACC,WAAW,EAAE,CAAA;EAClC,IAAA,IAAI,IAAI,CAACT,OAAO,CAACR,UAAU,EAAE;QAC3BkB,eAAM,CAACF,OAAO,CAAC,CAAA;EACjB,KAAA;EAEAA,IAAAA,OAAO,CAACG,SAAS,CAACC,GAAG,CAACzB,eAAe,CAAC,CAAA;MAEtC,IAAI,CAAC0B,iBAAiB,CAAC,MAAM;QAC3BP,gBAAO,CAACD,QAAQ,CAAC,CAAA;EACnB,KAAC,CAAC,CAAA;EACJ,GAAA;IAEAS,IAAIA,CAACT,QAAQ,EAAE;EACb,IAAA,IAAI,CAAC,IAAI,CAACL,OAAO,CAACP,SAAS,EAAE;QAC3Ba,gBAAO,CAACD,QAAQ,CAAC,CAAA;EACjB,MAAA,OAAA;EACF,KAAA;MAEA,IAAI,CAACI,WAAW,EAAE,CAACE,SAAS,CAACI,MAAM,CAAC5B,eAAe,CAAC,CAAA;MAEpD,IAAI,CAAC0B,iBAAiB,CAAC,MAAM;QAC3B,IAAI,CAACG,OAAO,EAAE,CAAA;QACdV,gBAAO,CAACD,QAAQ,CAAC,CAAA;EACnB,KAAC,CAAC,CAAA;EACJ,GAAA;EAEAW,EAAAA,OAAOA,GAAG;EACR,IAAA,IAAI,CAAC,IAAI,CAACd,WAAW,EAAE;EACrB,MAAA,OAAA;EACF,KAAA;MAEAe,YAAY,CAACC,GAAG,CAAC,IAAI,CAACf,QAAQ,EAAEf,eAAe,CAAC,CAAA;EAEhD,IAAA,IAAI,CAACe,QAAQ,CAACY,MAAM,EAAE,CAAA;MACtB,IAAI,CAACb,WAAW,GAAG,KAAK,CAAA;EAC1B,GAAA;;EAEA;EACAO,EAAAA,WAAWA,GAAG;EACZ,IAAA,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE;EAClB,MAAA,MAAMgB,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAA;EAC9CF,MAAAA,QAAQ,CAAC7B,SAAS,GAAG,IAAI,CAACU,OAAO,CAACV,SAAS,CAAA;EAC3C,MAAA,IAAI,IAAI,CAACU,OAAO,CAACR,UAAU,EAAE;EAC3B2B,QAAAA,QAAQ,CAACR,SAAS,CAACC,GAAG,CAAC1B,eAAe,CAAC,CAAA;EACzC,OAAA;QAEA,IAAI,CAACiB,QAAQ,GAAGgB,QAAQ,CAAA;EAC1B,KAAA;MAEA,OAAO,IAAI,CAAChB,QAAQ,CAAA;EACtB,GAAA;IAEAmB,iBAAiBA,CAACvB,MAAM,EAAE;EACxB;MACAA,MAAM,CAACL,WAAW,GAAG6B,mBAAU,CAACxB,MAAM,CAACL,WAAW,CAAC,CAAA;EACnD,IAAA,OAAOK,MAAM,CAAA;EACf,GAAA;EAEAQ,EAAAA,OAAOA,GAAG;MACR,IAAI,IAAI,CAACL,WAAW,EAAE;EACpB,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,MAAMM,OAAO,GAAG,IAAI,CAACC,WAAW,EAAE,CAAA;MAClC,IAAI,CAACT,OAAO,CAACN,WAAW,CAAC8B,MAAM,CAAChB,OAAO,CAAC,CAAA;EAExCS,IAAAA,YAAY,CAACQ,EAAE,CAACjB,OAAO,EAAEpB,eAAe,EAAE,MAAM;EAC9CkB,MAAAA,gBAAO,CAAC,IAAI,CAACN,OAAO,CAACT,aAAa,CAAC,CAAA;EACrC,KAAC,CAAC,CAAA;MAEF,IAAI,CAACW,WAAW,GAAG,IAAI,CAAA;EACzB,GAAA;IAEAW,iBAAiBA,CAACR,QAAQ,EAAE;EAC1BqB,IAAAA,+BAAsB,CAACrB,QAAQ,EAAE,IAAI,CAACI,WAAW,EAAE,EAAE,IAAI,CAACT,OAAO,CAACR,UAAU,CAAC,CAAA;EAC/E,GAAA;EACF;;;;;;;;"}