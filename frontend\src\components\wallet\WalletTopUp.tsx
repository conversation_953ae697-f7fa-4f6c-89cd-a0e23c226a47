import React, { useState } from 'react';
import {
  Con<PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  But<PERSON>,
  Form,
  Alert,
  Spinner,
  Badge,
} from 'react-bootstrap';
import creditService from '../../services/creditService';
import dattaAbleTheme from '../../theme/dattaAbleTheme';

interface WalletTopUpProps {
  onTopUpSuccess?: () => void;
  currentBalance: number;
}

const WalletTopUp: React.FC<WalletTopUpProps> = ({
  onTopUpSuccess,
  currentBalance,
}) => {
  const [amount, setAmount] = useState<number>(0);
  const [customAmount, setCustomAmount] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Predefined amounts
  const predefinedAmounts = [10, 20, 50, 100, 200, 500];

  const handleAmountSelect = (selectedAmount: number) => {
    setAmount(selectedAmount);
    setCustomAmount('');
    setError(null);
  };

  const handleCustomAmountChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setCustomAmount(value);
    
    const numericValue = parseFloat(value);
    if (!isNaN(numericValue) && numericValue > 0) {
      setAmount(numericValue);
      setError(null);
    } else {
      setAmount(0);
    }
  };

  const validateAmount = (): boolean => {
    if (amount <= 0) {
      setError('Please enter a valid amount');
      return false;
    }
    if (amount < 1) {
      setError('Minimum top-up amount is RM 1.00');
      return false;
    }
    if (amount > 10000) {
      setError('Maximum top-up amount is RM 10,000.00');
      return false;
    }
    return true;
  };

  const handleTopUp = async () => {
    if (!validateAmount()) {
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Create a temporary package object for the payment
      const tempPackage = {
        id: 999, // Temporary ID
        name: `RM ${amount.toFixed(2)} Top-up`,
        price: amount,
        credits: amount, // 1:1 conversion
      };

      const response = await creditService.createPayment(
        tempPackage.id,
        `${window.location.origin}/dashboard/wallet`
      );

      if (response.success && response.payment_url) {
        if (onTopUpSuccess) {
          onTopUpSuccess();
        }
        // Redirect to Billplz payment page
        window.location.href = response.payment_url;
      } else {
        setError(response.error || 'Payment creation failed');
      }
    } catch (error) {
      console.error('Top-up error:', error);
      setError('Failed to initiate payment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const isAmountSelected = amount > 0;
  const newBalance = currentBalance + amount;

  return (
    <div>
      <div className="d-flex align-items-center gap-3 mb-4">
        <div 
          className="d-flex align-items-center justify-content-center"
          style={{
            width: '48px',
            height: '48px',
            borderRadius: dattaAbleTheme.borderRadius.lg,
            backgroundColor: `${dattaAbleTheme.colors.primary.main}20`,
            color: dattaAbleTheme.colors.primary.main,
          }}
        >
          <i className="fas fa-plus"></i>
        </div>
        <h5 className="mb-0 fw-semibold">Top Up Your Wallet</h5>
      </div>

      <Row>
        {/* Amount Selection */}
        <Col lg={8}>
          <Card className="border-0 shadow-sm mb-4" style={{ borderRadius: dattaAbleTheme.borderRadius.lg }}>
            <Card.Body className="p-4">
              <h6 className="fw-semibold mb-3">Select Amount</h6>

              {/* Predefined Amounts */}
              <div className="mb-4">
                <small className="text-muted mb-3 d-block">Quick Select (RM)</small>
                <Row className="g-2">
                  {predefinedAmounts.map((presetAmount) => (
                    <Col xs={6} sm={4} md={3} key={presetAmount}>
                      <Button
                        variant={amount === presetAmount ? 'primary' : 'outline-secondary'}
                        className="w-100 fw-semibold"
                        style={{
                          height: '48px',
                          borderRadius: dattaAbleTheme.borderRadius.md,
                          transition: 'all 0.3s ease',
                        }}
                        onClick={() => handleAmountSelect(presetAmount)}
                      >
                        RM {presetAmount}
                      </Button>
                    </Col>
                  ))}
                </Row>
              </div>

              {/* Custom Amount */}
              <div>
                <small className="text-muted mb-2 d-block">Or Enter Custom Amount</small>
                <Form.Group>
                  <Form.Control
                    type="number"
                    placeholder="Enter amount between RM 1.00 - RM 10,000.00"
                    value={customAmount}
                    onChange={handleCustomAmountChange}
                    min={1}
                    max={10000}
                    step={0.01}
                    style={{ borderRadius: dattaAbleTheme.borderRadius.md }}
                  />
                  <Form.Text className="text-muted">
                    Enter amount between RM 1.00 - RM 10,000.00
                  </Form.Text>
                </Form.Group>
              </div>

              {error && (
                <Alert variant="danger" className="mt-3" style={{ borderRadius: dattaAbleTheme.borderRadius.md }}>
                  {error}
                </Alert>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Summary & Payment */}
        <Col lg={4}>
          <Card 
            className="border-0 shadow-sm position-sticky"
            style={{ 
              borderRadius: dattaAbleTheme.borderRadius.lg,
              top: '20px',
              backgroundColor: dattaAbleTheme.colors.background.light,
            }}
          >
            <Card.Body className="p-4">
              <h6 className="fw-semibold mb-3">Payment Summary</h6>

              <div className="mb-3">
                <div className="d-flex justify-content-between mb-2">
                  <small className="text-muted">Current Balance</small>
                  <small className="fw-semibold">{creditService.formatWalletBalance(currentBalance)}</small>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <small className="text-muted">Top-up Amount</small>
                  <small className="fw-semibold text-primary">+{creditService.formatWalletBalance(amount)}</small>
                </div>
                <hr />
                <div className="d-flex justify-content-between">
                  <span className="fw-semibold">New Balance</span>
                  <span className="fw-bold text-success">{creditService.formatWalletBalance(newBalance)}</span>
                </div>
              </div>

              <Button
                variant="primary"
                size="lg"
                className="w-100 fw-semibold mb-3"
                onClick={handleTopUp}
                disabled={!isAmountSelected || loading}
                style={{
                  borderRadius: dattaAbleTheme.borderRadius.md,
                  padding: `${dattaAbleTheme.spacing[3]} ${dattaAbleTheme.spacing[4]}`,
                }}
              >
                {loading ? (
                  <>
                    <Spinner animation="border" size="sm" className="me-2" />
                    Processing...
                  </>
                ) : (
                  <>
                    <i className="fas fa-credit-card me-2"></i>
                    Pay RM {amount.toFixed(2)}
                  </>
                )}
              </Button>

              {/* Payment Features */}
              <div>
                <h6 className="fw-semibold mb-3 text-muted">Payment Features</h6>
                <div className="d-flex flex-column gap-2">
                  <div className="d-flex align-items-center gap-2">
                    <i className="fas fa-shield-alt text-success"></i>
                    <small>Secure Billplz Payment</small>
                  </div>
                  <div className="d-flex align-items-center gap-2">
                    <i className="fas fa-bolt text-success"></i>
                    <small>Instant Balance Update</small>
                  </div>
                  <div className="d-flex align-items-center gap-2">
                    <i className="fas fa-check-circle text-success"></i>
                    <small>1:1 RM Conversion</small>
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Information Section */}
      <Card 
        className="border-0 mt-4"
        style={{
          borderRadius: dattaAbleTheme.borderRadius.lg,
          backgroundColor: `${dattaAbleTheme.colors.primary.main}15`,
        }}
      >
        <Card.Body className="p-4">
          <h6 className="fw-semibold mb-3">How It Works</h6>
          <Row>
            <Col sm={4} className="text-center mb-3">
              <div 
                className="d-flex align-items-center justify-content-center mx-auto mb-3"
                style={{
                  width: '60px',
                  height: '60px',
                  borderRadius: '50%',
                  backgroundColor: dattaAbleTheme.colors.primary.main,
                  color: 'white',
                }}
              >
                <span className="fw-bold">1</span>
              </div>
              <h6 className="fw-semibold mb-2">Select Amount</h6>
              <small className="text-muted">Choose from preset amounts or enter a custom value</small>
            </Col>
            <Col sm={4} className="text-center mb-3">
              <div 
                className="d-flex align-items-center justify-content-center mx-auto mb-3"
                style={{
                  width: '60px',
                  height: '60px',
                  borderRadius: '50%',
                  backgroundColor: dattaAbleTheme.colors.primary.main,
                  color: 'white',
                }}
              >
                <span className="fw-bold">2</span>
              </div>
              <h6 className="fw-semibold mb-2">Secure Payment</h6>
              <small className="text-muted">Complete payment through Billplz secure gateway</small>
            </Col>
            <Col sm={4} className="text-center mb-3">
              <div 
                className="d-flex align-items-center justify-content-center mx-auto mb-3"
                style={{
                  width: '60px',
                  height: '60px',
                  borderRadius: '50%',
                  backgroundColor: dattaAbleTheme.colors.primary.main,
                  color: 'white',
                }}
              >
                <span className="fw-bold">3</span>
              </div>
              <h6 className="fw-semibold mb-2">Instant Update</h6>
              <small className="text-muted">Your wallet balance is updated immediately</small>
            </Col>
          </Row>
        </Card.Body>
      </Card>
    </div>
  );
};

export default WalletTopUp;
