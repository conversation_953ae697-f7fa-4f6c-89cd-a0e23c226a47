import React from 'react';
import {
  Row,
  Col,
  Card,
  Button,
} from 'react-bootstrap';
import dattaAbleTheme from '../../theme/dattaAbleTheme';

interface WalletQuickActionsProps {
  onTopUpClick: () => void;
  onHistoryClick: () => void;
  currentBalance: number;
}

const WalletQuickActions: React.FC<WalletQuickActionsProps> = ({
  onTopUpClick,
  onHistoryClick,
  currentBalance,
}) => {
  const quickActions = [
    {
      title: 'Top Up Wallet',
      description: 'Add money to your wallet',
      icon: 'fas fa-plus',
      color: 'primary',
      action: onTopUpClick,
      disabled: false,
    },
    {
      title: 'View History',
      description: 'Check transaction history',
      icon: 'fas fa-history',
      color: 'secondary',
      action: onHistoryClick,
      disabled: false,
    },
    {
      title: 'Download Receipt',
      description: 'Get transaction receipts',
      icon: 'fas fa-receipt',
      color: 'info',
      action: () => {
        // TODO: Implement receipt download
        console.log('Download receipt functionality to be implemented');
      },
      disabled: true,
    },
    {
      title: 'Wallet Settings',
      description: 'Manage wallet preferences',
      icon: 'fas fa-wallet',
      color: 'warning',
      action: () => {
        // TODO: Implement wallet settings
        console.log('Wallet settings functionality to be implemented');
      },
      disabled: true,
    },
  ];

  return (
    <Card
      className="border-0 shadow-sm mb-4"
      style={{ borderRadius: dattaAbleTheme.borderRadius.lg }}
    >
      <Card.Body className="p-4">
        <div className="mb-3">
          <h6 className="fw-semibold mb-1">Quick Actions</h6>
          <small className="text-muted">
            Manage your wallet with these convenient shortcuts
          </small>
        </div>

        <Row className="g-3">
          {quickActions.map((action, index) => (
            <Col xs={12} sm={6} md={3} key={index}>
              <Card
                className={`text-center h-100 ${action.disabled ? '' : 'cursor-pointer'}`}
                style={{
                  borderRadius: dattaAbleTheme.borderRadius.lg,
                  opacity: action.disabled ? 0.6 : 1,
                  transition: 'all 0.3s ease',
                  cursor: action.disabled ? 'not-allowed' : 'pointer',
                }}
                onClick={action.disabled ? undefined : action.action}
              >
                <Card.Body className="p-3">
                  <div
                    className="d-flex align-items-center justify-content-center mx-auto mb-2"
                    style={{
                      width: '64px',
                      height: '64px',
                      borderRadius: '50%',
                      backgroundColor: `${(dattaAbleTheme.colors as any)[action.color].main}20`,
                      color: (dattaAbleTheme.colors as any)[action.color].main,
                    }}
                  >
                    <i className={action.icon} style={{ fontSize: '2rem' }}></i>
                  </div>
                  <h6 className="fw-semibold mb-1">{action.title}</h6>
                  <small className="text-muted">{action.description}</small>
                  {action.disabled && (
                    <div className="mt-1">
                      <small className="text-muted">Coming Soon</small>
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>

        {/* Balance Status */}
        <div
          className="mt-4 p-3"
          style={{
            borderRadius: dattaAbleTheme.borderRadius.lg,
            backgroundColor: currentBalance > 0 ? `${dattaAbleTheme.colors.success.main}20` : `${dattaAbleTheme.colors.warning.main}20`,
            color: currentBalance > 0 ? dattaAbleTheme.colors.success.main : dattaAbleTheme.colors.warning.main,
          }}
        >
          <div className="d-flex align-items-center gap-3 mb-2">
            <i
              className={currentBalance > 0 ? 'fas fa-chart-line' : 'fas fa-credit-card'}
              style={{ fontSize: '2rem' }}
            ></i>
            <div>
              <h6 className="fw-semibold mb-1">
                {currentBalance > 0 ? 'Wallet Active' : 'Wallet Empty'}
              </h6>
              <small style={{ opacity: 0.8 }}>
                {currentBalance > 0
                  ? 'Your wallet is ready for transactions'
                  : 'Add money to start using services'}
              </small>
            </div>
          </div>
          {currentBalance <= 0 && (
            <Button
              variant="warning"
              onClick={onTopUpClick}
              className="d-flex align-items-center gap-2"
            >
              <i className="fas fa-plus"></i>
              Add Money Now
            </Button>
          )}
        </div>
      </Card.Body>
    </Card>
  );
};

export default WalletQuickActions;
