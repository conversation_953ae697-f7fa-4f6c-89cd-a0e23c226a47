{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DattaAbleFooter.jsx\";\nimport React from 'react';\nimport { Container, Row, Col } from 'react-bootstrap';\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DattaAbleFooter = () => {\n  const footerStyles = {\n    backgroundColor: dattaAbleTheme.colors.background.paper,\n    borderTop: `1px solid ${dattaAbleTheme.colors.border}`,\n    padding: `${dattaAbleTheme.spacing[4]} 0`,\n    marginTop: 'auto'\n  };\n  const textStyles = {\n    fontSize: dattaAbleTheme.typography.fontSize.sm,\n    color: dattaAbleTheme.colors.text.secondary,\n    margin: 0\n  };\n  const linkStyles = {\n    color: dattaAbleTheme.colors.primary.main,\n    textDecoration: 'none',\n    fontWeight: dattaAbleTheme.typography.fontWeight.medium\n  };\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    style: footerStyles,\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            style: textStyles,\n            children: [\"\\xA9 \", new Date().getFullYear(), \" Made with\", ' ', /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-heart text-danger\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), \" by\", ' ', /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              style: linkStyles,\n              onMouseEnter: e => {\n                e.target.style.textDecoration = 'underline';\n              },\n              onMouseLeave: e => {\n                e.target.style.textDecoration = 'none';\n              },\n              children: \"Your Company\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          className: \"text-md-end\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            style: textStyles,\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              style: linkStyles,\n              className: \"me-3\",\n              onMouseEnter: e => {\n                e.target.style.textDecoration = 'underline';\n              },\n              onMouseLeave: e => {\n                e.target.style.textDecoration = 'none';\n              },\n              children: \"About\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              style: linkStyles,\n              className: \"me-3\",\n              onMouseEnter: e => {\n                e.target.style.textDecoration = 'underline';\n              },\n              onMouseLeave: e => {\n                e.target.style.textDecoration = 'none';\n              },\n              children: \"Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              style: linkStyles,\n              onMouseEnter: e => {\n                e.target.style.textDecoration = 'underline';\n              },\n              onMouseLeave: e => {\n                e.target.style.textDecoration = 'none';\n              },\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_c = DattaAbleFooter;\nexport default DattaAbleFooter;\nvar _c;\n$RefreshReg$(_c, \"DattaAbleFooter\");", "map": {"version": 3, "names": ["React", "Container", "Row", "Col", "dattaAbleTheme", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "footerStyles", "backgroundColor", "colors", "background", "paper", "borderTop", "border", "padding", "spacing", "marginTop", "textStyles", "fontSize", "typography", "sm", "color", "text", "secondary", "margin", "linkStyles", "primary", "main", "textDecoration", "fontWeight", "medium", "style", "children", "fluid", "className", "md", "Date", "getFullYear", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onMouseEnter", "e", "target", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/DattaAbleFooter.jsx"], "sourcesContent": ["import React from 'react';\nimport { Container, Row, Col } from 'react-bootstrap';\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\n\nconst DattaAbleFooter = () => {\n  const footerStyles = {\n    backgroundColor: dattaAbleTheme.colors.background.paper,\n    borderTop: `1px solid ${dattaAbleTheme.colors.border}`,\n    padding: `${dattaAbleTheme.spacing[4]} 0`,\n    marginTop: 'auto',\n  };\n\n  const textStyles = {\n    fontSize: dattaAbleTheme.typography.fontSize.sm,\n    color: dattaAbleTheme.colors.text.secondary,\n    margin: 0,\n  };\n\n  const linkStyles = {\n    color: dattaAbleTheme.colors.primary.main,\n    textDecoration: 'none',\n    fontWeight: dattaAbleTheme.typography.fontWeight.medium,\n  };\n\n  return (\n    <footer style={footerStyles}>\n      <Container fluid>\n        <Row className=\"align-items-center\">\n          <Col md={6}>\n            <p style={textStyles}>\n              © {new Date().getFullYear()} Made with{' '}\n              <i className=\"fas fa-heart text-danger\"></i> by{' '}\n              <a\n                href=\"#\"\n                style={linkStyles}\n                onMouseEnter={(e) => {\n                  e.target.style.textDecoration = 'underline';\n                }}\n                onMouseLeave={(e) => {\n                  e.target.style.textDecoration = 'none';\n                }}\n              >\n                Your Company\n              </a>\n            </p>\n          </Col>\n          <Col md={6} className=\"text-md-end\">\n            <p style={textStyles}>\n              <a\n                href=\"#\"\n                style={linkStyles}\n                className=\"me-3\"\n                onMouseEnter={(e) => {\n                  e.target.style.textDecoration = 'underline';\n                }}\n                onMouseLeave={(e) => {\n                  e.target.style.textDecoration = 'none';\n                }}\n              >\n                About\n              </a>\n              <a\n                href=\"#\"\n                style={linkStyles}\n                className=\"me-3\"\n                onMouseEnter={(e) => {\n                  e.target.style.textDecoration = 'underline';\n                }}\n                onMouseLeave={(e) => {\n                  e.target.style.textDecoration = 'none';\n                }}\n              >\n                Support\n              </a>\n              <a\n                href=\"#\"\n                style={linkStyles}\n                onMouseEnter={(e) => {\n                  e.target.style.textDecoration = 'underline';\n                }}\n                onMouseLeave={(e) => {\n                  e.target.style.textDecoration = 'none';\n                }}\n              >\n                Contact\n              </a>\n            </p>\n          </Col>\n        </Row>\n      </Container>\n    </footer>\n  );\n};\n\nexport default DattaAbleFooter;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AACrD,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMC,YAAY,GAAG;IACnBC,eAAe,EAAEL,cAAc,CAACM,MAAM,CAACC,UAAU,CAACC,KAAK;IACvDC,SAAS,EAAE,aAAaT,cAAc,CAACM,MAAM,CAACI,MAAM,EAAE;IACtDC,OAAO,EAAE,GAAGX,cAAc,CAACY,OAAO,CAAC,CAAC,CAAC,IAAI;IACzCC,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,UAAU,GAAG;IACjBC,QAAQ,EAAEf,cAAc,CAACgB,UAAU,CAACD,QAAQ,CAACE,EAAE;IAC/CC,KAAK,EAAElB,cAAc,CAACM,MAAM,CAACa,IAAI,CAACC,SAAS;IAC3CC,MAAM,EAAE;EACV,CAAC;EAED,MAAMC,UAAU,GAAG;IACjBJ,KAAK,EAAElB,cAAc,CAACM,MAAM,CAACiB,OAAO,CAACC,IAAI;IACzCC,cAAc,EAAE,MAAM;IACtBC,UAAU,EAAE1B,cAAc,CAACgB,UAAU,CAACU,UAAU,CAACC;EACnD,CAAC;EAED,oBACEzB,OAAA;IAAQ0B,KAAK,EAAExB,YAAa;IAAAyB,QAAA,eAC1B3B,OAAA,CAACL,SAAS;MAACiC,KAAK;MAAAD,QAAA,eACd3B,OAAA,CAACJ,GAAG;QAACiC,SAAS,EAAC,oBAAoB;QAAAF,QAAA,gBACjC3B,OAAA,CAACH,GAAG;UAACiC,EAAE,EAAE,CAAE;UAAAH,QAAA,eACT3B,OAAA;YAAG0B,KAAK,EAAEd,UAAW;YAAAe,QAAA,GAAC,OAClB,EAAC,IAAII,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAC,YAAU,EAAC,GAAG,eAC1ChC,OAAA;cAAG6B,SAAS,EAAC;YAA0B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,OAAG,EAAC,GAAG,eACnDpC,OAAA;cACEqC,IAAI,EAAC,GAAG;cACRX,KAAK,EAAEN,UAAW;cAClBkB,YAAY,EAAGC,CAAC,IAAK;gBACnBA,CAAC,CAACC,MAAM,CAACd,KAAK,CAACH,cAAc,GAAG,WAAW;cAC7C,CAAE;cACFkB,YAAY,EAAGF,CAAC,IAAK;gBACnBA,CAAC,CAACC,MAAM,CAACd,KAAK,CAACH,cAAc,GAAG,MAAM;cACxC,CAAE;cAAAI,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNpC,OAAA,CAACH,GAAG;UAACiC,EAAE,EAAE,CAAE;UAACD,SAAS,EAAC,aAAa;UAAAF,QAAA,eACjC3B,OAAA;YAAG0B,KAAK,EAAEd,UAAW;YAAAe,QAAA,gBACnB3B,OAAA;cACEqC,IAAI,EAAC,GAAG;cACRX,KAAK,EAAEN,UAAW;cAClBS,SAAS,EAAC,MAAM;cAChBS,YAAY,EAAGC,CAAC,IAAK;gBACnBA,CAAC,CAACC,MAAM,CAACd,KAAK,CAACH,cAAc,GAAG,WAAW;cAC7C,CAAE;cACFkB,YAAY,EAAGF,CAAC,IAAK;gBACnBA,CAAC,CAACC,MAAM,CAACd,KAAK,CAACH,cAAc,GAAG,MAAM;cACxC,CAAE;cAAAI,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJpC,OAAA;cACEqC,IAAI,EAAC,GAAG;cACRX,KAAK,EAAEN,UAAW;cAClBS,SAAS,EAAC,MAAM;cAChBS,YAAY,EAAGC,CAAC,IAAK;gBACnBA,CAAC,CAACC,MAAM,CAACd,KAAK,CAACH,cAAc,GAAG,WAAW;cAC7C,CAAE;cACFkB,YAAY,EAAGF,CAAC,IAAK;gBACnBA,CAAC,CAACC,MAAM,CAACd,KAAK,CAACH,cAAc,GAAG,MAAM;cACxC,CAAE;cAAAI,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJpC,OAAA;cACEqC,IAAI,EAAC,GAAG;cACRX,KAAK,EAAEN,UAAW;cAClBkB,YAAY,EAAGC,CAAC,IAAK;gBACnBA,CAAC,CAACC,MAAM,CAACd,KAAK,CAACH,cAAc,GAAG,WAAW;cAC7C,CAAE;cACFkB,YAAY,EAAGF,CAAC,IAAK;gBACnBA,CAAC,CAACC,MAAM,CAACd,KAAK,CAACH,cAAc,GAAG,MAAM;cACxC,CAAE;cAAAI,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEb,CAAC;AAACM,EAAA,GAxFIzC,eAAe;AA0FrB,eAAeA,eAAe;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}